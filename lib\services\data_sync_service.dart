import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:path_provider/path_provider.dart';
import '../models/event.dart';
import '../models/product.dart';
import '../models/seller.dart';
import '../models/prepayment.dart';
import '../models/sales_log.dart';
import '../models/transaction_type.dart';
import '../models/prepayment_virtual_product.dart';
import '../models/prepayment_product_link.dart';
import '../models/category.dart' as model_category;
import '../models/user_settings.dart';
import '../models/checklist_template.dart';
import '../models/checklist_item.dart';
import '../models/revenue_goal.dart';
import '../models/set_discount.dart';
import '../models/set_discount_transaction.dart';
import '../repositories/event_repository.dart';
import '../repositories/set_discount_repository.dart';
import '../repositories/set_discount_transaction_repository.dart';

import '../repositories/product_repository.dart';
import '../repositories/seller_repository.dart';
import '../repositories/prepayment_repository.dart';
import '../repositories/sales_log_repository.dart';
import '../utils/image_sync_utils.dart';
import '../utils/firebase_upload_utils.dart';
import '../repositories/prepayment_product_link_repository.dart';
import '../repositories/revenue_goal_repository.dart';
import '../repositories/checklist_repository.dart';
import '../services/database_service.dart';
import '../services/usage_tracking_service.dart';
import '../utils/logger_utils.dart';

import '../utils/local_data_cleaner.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/realtime_sync_service_main.dart';
import '../services/subscription_service.dart';
import '../models/subscription_plan.dart';

/// Firebase Firestore와 로컬 데이터베이스 간의 동기화를 담당하는 서비스
class DataSyncService {
  static const String _tag = 'DataSyncService';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final DatabaseService _databaseService;
  final RealtimeSyncService _realtimeSyncService;
  final SubscriptionService _subscriptionService = SubscriptionService();
  final UsageTrackingService _usageTrackingService = UsageTrackingService();

  // 동기화 보호 장치
  final Map<String, bool> _syncLocks = {};
  final Map<String, Completer<void>> _syncCompleters = {};

  late final EventRepository _eventRepository;
  late final ProductRepository _productRepository;
  late final SellerRepository _sellerRepository;
  late final PrepaymentRepository _prepaymentRepository;
  late final SalesLogRepository _salesLogRepository;
  late final PrepaymentProductLinkRepository _prepaymentProductLinkRepository;
  late final ChecklistRepository _checklistRepository;
  late final SetDiscountTransactionRepository _setDiscountTransactionRepository;

  
  // 백그라운드 이미지 업로드 큐 (상품용)
  final List<_ImageUploadTask> _imageUploadQueue = [];
  bool _isProcessingImageQueue = false;
  Timer? _queueProcessingTimer;
  
  // 백그라운드 이미지 업로드 큐 (행사용)
  final List<_EventImageUploadTask> _eventImageUploadQueue = [];
  bool _isProcessingEventImageQueue = false;
  Timer? _eventQueueProcessingTimer;
  
  // 이미지 다운로드 동시성 제어
  final Set<String> _downloadingImages = {};
  int _currentDownloads = 0;
  
  DataSyncService(this._databaseService, this._realtimeSyncService) {
    _initializeRepositories();
    _startImageUploadQueueProcessor();
    _startEventImageUploadQueueProcessor();
  }
  
  void _initializeRepositories() {
    _eventRepository = EventRepository(_databaseService);
    _productRepository = ProductRepository(database: _databaseService);
    _sellerRepository = SellerRepository(database: _databaseService);
    _prepaymentRepository = PrepaymentRepository(database: _databaseService);
    _salesLogRepository = SalesLogRepository(database: _databaseService);
    _prepaymentProductLinkRepository = PrepaymentProductLinkRepository(database: _databaseService);
    _checklistRepository = ChecklistRepository(_databaseService);
    _setDiscountTransactionRepository = SetDiscountTransactionRepository();

  }

  /// 현재 로그인된 사용자의 UID를 가져옵니다
  String? get _currentUserId => _auth.currentUser?.uid;

  /// 사용자가 로그인되어 있는지 확인합니다
  bool get isUserLoggedIn => _currentUserId != null;

  /// 실시간 동기화가 활성화되어 있는지 확인합니다
  bool get _isRealtimeSyncEnabled => _realtimeSyncService.realtimeSyncEnabled;

  /// 실시간 동기화 기능 사용 가능 여부 확인 (플랜별)
  Future<bool> get _canUseRealtimeSync async {
    try {
      final planType = await _subscriptionService.getCurrentPlanType();

      // 프로 플랜만 실시간 동기화 사용 가능
      return planType == SubscriptionPlanType.pro;
    } catch (e) {
      LoggerUtils.logError('플랜 확인 실패, 실시간 동기화 비활성화', tag: _tag, error: e);
      return false;
    }
  }

  /// 서버에 사용자 데이터가 존재하는지 확인합니다
  Future<bool> hasServerData() async {
    try {
      return await executeWithAuthErrorHandling('서버 데이터 존재 여부 확인', () async {
        if (!isUserLoggedIn) return false;
        
        // 행사 데이터 확인 (EventWorkspace는 Event로 저장됨)
        final eventDoc = await _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .limit(1)
            .get();
        
        final hasData = eventDoc.docs.isNotEmpty;
        LoggerUtils.logInfo('서버 데이터 존재 여부: $hasData (문서 개수: ${eventDoc.docs.length})', tag: _tag);

        if (hasData) {
          LoggerUtils.logInfo('발견된 첫 번째 이벤트 문서 ID: ${eventDoc.docs.first.id}', tag: _tag);
        }

        return hasData;
      });
    } catch (e) {
      LoggerUtils.logError('서버 데이터 확인 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 동기화 보호 장치 - 특정 키에 대한 동기화 잠금
  Future<T> _withSyncLock<T>(String lockKey, Future<T> Function() operation) async {
    // 이미 동기화 중인 경우 대기
    while (_syncLocks[lockKey] == true) {
      final completer = _syncCompleters[lockKey];
      if (completer != null) {
        await completer.future;
      } else {
        // 안전장치: completer가 없으면 잠시 대기
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }

    // 동기화 시작
    _syncLocks[lockKey] = true;
    final completer = Completer<void>();
    _syncCompleters[lockKey] = completer;

    try {
      LoggerUtils.logDebug('동기화 잠금 획득: $lockKey', tag: _tag);
      final result = await operation();
      return result;
    } finally {
      // 동기화 완료
      _syncLocks[lockKey] = false;
      _syncCompleters.remove(lockKey);
      completer.complete();
      LoggerUtils.logDebug('동기화 잠금 해제: $lockKey', tag: _tag);
    }
  }

  /// 서버에 사용자 계정이 유효한지 확인합니다
  /// 로컬에 인증 상태가 있지만 서버에 계정이 삭제된 경우를 감지합니다
  Future<bool> validateUserAccount() async {
    try {
      return await executeWithAuthErrorHandling('사용자 계정 유효성 확인', () async {
        if (!isUserLoggedIn) return false;
        
        // Firestore에서 사용자 문서 존재 여부 확인
        final userDoc = await _firestore
            .collection('users')
            .doc(_currentUserId!)
            .get();
        
        final isValid = userDoc.exists;
        LoggerUtils.logInfo('사용자 계정 유효성: $isValid', tag: _tag);
        
        if (!isValid) {
          LoggerUtils.logWarning('서버에 사용자 계정이 존재하지 않음 - 로컬 인증 상태와 불일치', tag: _tag);
        }
        
        return isValid;
      });
    } catch (e) {
      LoggerUtils.logError('사용자 계정 유효성 확인 실패', tag: _tag, error: e);
      
      // Firebase Auth 관련 오류인지 확인
      if (_isAuthenticationError(e)) {
        LoggerUtils.logWarning('인증 오류 감지 - 계정이 유효하지 않은 것으로 판단', tag: _tag);
        return false;
      }
      
      // 네트워크 오류 등의 경우 일시적 문제로 간주하여 true 반환
      return true;
    }
  }

  /// Firebase 인증 관련 오류인지 확인
  /// Firestore 규칙 권한 오류와 실제 Auth 토큰 오류를 구분
  bool _isAuthenticationError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Firestore 규칙 권한 오류는 제외 (이는 Auth 토큰 문제가 아님)
    if (errorString.contains('permission-denied') ||
        errorString.contains('missing-or-insufficient-permissions') ||
        errorString.contains('insufficient-permission')) {
      // Firestore 규칙 권한 오류는 Auth 토큰 문제가 아니므로 로그아웃하지 않음
      LoggerUtils.logInfo('Firestore 규칙 권한 오류 감지 - Auth 토큰 문제가 아니므로 로그아웃하지 않음', tag: _tag);
      return false;
    }

    // 실제 Firebase Auth 토큰 관련 오류만 처리
    final authTokenErrorPatterns = [
      'unauthenticated',
      'user-not-found',
      'invalid-user-token',
      'user-token-expired',
      'token-expired',
      'auth/user-not-found',
      'auth/invalid-user-token',
      'auth/user-token-expired',
      'auth/user-disabled',
      'auth/user-token-expired',
      'auth/invalid-credential',
    ];

    return authTokenErrorPatterns.any((pattern) => errorString.contains(pattern));
  }

  /// 계정 삭제 관련 오류인지 확인 (사용자 문서 없음)
  bool _isAccountDeletionError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // 계정 삭제를 나타내는 오류 패턴들
    final accountDeletionPatterns = [
      'user-not-found',
      'auth/user-not-found',
      // Firestore에서 사용자 문서가 없을 때
      'document does not exist',
      'no document to update',
    ];
    
    return accountDeletionPatterns.any((pattern) => errorString.contains(pattern));
  }

  /// 닉네임 Provider 초기화가 필요함을 알리는 플래그 설정
  Future<void> _setNicknameResetFlag() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('nickname_reset_required', true);
      LoggerUtils.logInfo('닉네임 초기화 플래그 설정 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('닉네임 초기화 플래그 설정 실패', tag: _tag, error: e);
    }
  }

  /// API 호출 중 인증 오류가 발생했을 때 자동 로그아웃 처리
  Future<void> handleAuthenticationError(dynamic error) async {
    if (!_isAuthenticationError(error)) return;
    
    LoggerUtils.logWarning('API 호출 중 인증 오류 감지 - 자동 로그아웃 및 로컬 데이터 정리 시작', tag: _tag);
    
    try {
      // 1. Firebase 로그아웃
      await _auth.signOut();
      LoggerUtils.logInfo('Firebase 로그아웃 완료', tag: _tag);
      
      // 2. 로컬 데이터 정리 (계정 삭제 관련 오류인지 확인하여 적절한 정리 방식 선택)
      if (_isAccountDeletionError(error)) {
        await LocalDataCleaner.clearDataForAccountDeletion();
        LoggerUtils.logInfo('계정 삭제 관련 오류로 인한 완전 데이터 정리 완료', tag: _tag);
      } else {
        await LocalDataCleaner.clearDataForLogout();
        LoggerUtils.logInfo('일반 인증 오류로 인한 로그아웃 데이터 정리 완료', tag: _tag);
        
        // 닉네임 Provider 초기화를 위해 별도 플래그 설정
        await _setNicknameResetFlag();
      }
      
      LoggerUtils.logInfo('인증 오류로 인한 자동 로그아웃 및 데이터 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('자동 로그아웃 처리 실패', tag: _tag, error: e);
      
      // 로컬 데이터 정리에 실패해도 Firebase 로그아웃은 시도
      try {
        await _auth.signOut();
        LoggerUtils.logInfo('Firebase 로그아웃만 완료 (로컬 데이터 정리 실패)', tag: _tag);
      } catch (authError) {
        LoggerUtils.logError('Firebase 로그아웃도 실패', tag: _tag, error: authError);
      }
    }
  }

  /// 인증 오류 감지 및 자동 처리가 포함된 안전한 Firebase API 호출 래퍼
  Future<T> executeWithAuthErrorHandling<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    try {
      LoggerUtils.logDebug('$operationName 실행 시작', tag: _tag);
      final result = await operation();
      LoggerUtils.logDebug('$operationName 실행 완료', tag: _tag);
      return result;
    } catch (e) {
      LoggerUtils.logError('$operationName 실행 중 오류 발생', tag: _tag, error: e);
      
      // 인증 오류인 경우 자동 로그아웃 처리
      await handleAuthenticationError(e);
      
      // 원본 오류를 다시 throw
      rethrow;
    }
  }

  /// 서버에서 행사 목록을 가져옵니다
  Future<List<Event>> getServerEvents() async {
    return await executeWithAuthErrorHandling('서버 행사 목록 조회', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');
      
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .get();
      
      final events = <Event>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id); // Firestore 문서 ID를 int로 변환
          events.add(Event.fromFirebaseMap(data));
        } catch (e) {
          LoggerUtils.logWarning('행사 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }
      
      LoggerUtils.logInfo('서버 행사 목록 조회 완료: ${events.length}개', tag: _tag);
      return events;
    });
  }

  /// 개별 상품을 즉시 Firebase에 업로드 (백그라운드 이미지 업로드)
  Future<void> uploadSingleProduct(Product product) async {
    return await executeWithAuthErrorHandling('상품 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 또는 플랜에서 지원하지 않는 경우 업로드 건너뛰기
      final canUseRealtimeSync = await _canUseRealtimeSync;
      if (!_isRealtimeSyncEnabled || !canUseRealtimeSync) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 상품 업로드를 건너뜁니다: ${product.name}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('개별 상품 업로드 시작: ${product.name}', tag: _tag);

      // 1단계: 상품 데이터부터 즉시 업로드 (이미지 없이)
      Product productToUpload = product;
      String? localImagePath;
      
      if (product.imagePath != null && 
          product.imagePath!.isNotEmpty && 
          ImageSyncUtils.isLocalImagePath(product.imagePath!)) {
        // 로컬 이미지 경로 저장하고 일단 null로 설정
        localImagePath = product.imagePath!;
        productToUpload = product.copyWith(imagePath: null);
      }

      // 상품 데이터를 Firestore에 즉시 업로드
      final productData = productToUpload.toJson();
      productData.remove('id');

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(product.eventId.toString())
          .collection('products')
          .doc(product.id.toString())
          .set(productData);

      LoggerUtils.logInfo('상품 데이터 즉시 업로드 완료: ${product.name}', tag: _tag);

      // 2단계: 이미지가 있다면 백그라운드 큐에 추가
      if (localImagePath != null) {
        _addImageUploadTask(product, localImagePath);
        LoggerUtils.logInfo('이미지 업로드를 백그라운드 큐에 추가: ${product.name}', tag: _tag);
      }
    });
  }

  /// 개별 카테고리를 즉시 Firebase에 업로드 (실시간 동기화용)
  Future<void> uploadSingleCategory(model_category.Category category) async {
    return await executeWithAuthErrorHandling('카테고리 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 또는 플랜에서 지원하지 않는 경우 업로드 건너뛰기
      final canUseRealtimeSync = await _canUseRealtimeSync;
      if (!_isRealtimeSyncEnabled || !canUseRealtimeSync) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 카테고리 업로드를 건너뜁니다: ${category.name}', tag: _tag);
        return;
      }

      // 유효한 ID가 없으면 업로드하지 않음 (음수 ID나 null ID는 임시 카테고리)
      if (category.id == null || category.id! < 1) {
        LoggerUtils.logWarning('유효하지 않은 카테고리 ID로 인해 업로드 건너뜀: ${category.name} (ID: ${category.id})', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('개별 카테고리 업로드 시작: ${category.name} (ID: ${category.id})', tag: _tag);

      final categoryData = category.toJson();
      categoryData.remove('id');

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(category.eventId.toString())
          .collection('categories')
          .doc(category.id.toString())
          .set(categoryData);

      LoggerUtils.logInfo('카테고리 데이터 업로드 완료: ${category.name} (ID: ${category.id})', tag: _tag);
    });
  }

  /// Firebase에서 최신 카테고리 목록을 다운로드하여 로컬 DB와 동기화 (실시간 동기화용)
  Future<void> downloadCategoriesFromFirebase(int eventId) async {
    return await executeWithAuthErrorHandling('카테고리 목록 다운로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('Firebase에서 최신 카테고리 목록 다운로드 시작: eventId $eventId', tag: _tag);
      await _downloadCategories(eventId);
      LoggerUtils.logInfo('Firebase에서 최신 카테고리 목록 다운로드 완료: eventId $eventId', tag: _tag);
    });
  }

  /// 개별 카테고리를 즉시 Firebase에서 삭제 (실시간 동기화용)
  Future<void> deleteSingleCategory(model_category.Category category) async {
    return await executeWithAuthErrorHandling('카테고리 삭제', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('개별 카테고리 즉시 삭제 시작: ${category.name}', tag: _tag);

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(category.eventId.toString())
          .collection('categories')
          .doc(category.id.toString())
          .delete();

      LoggerUtils.logInfo('개별 카테고리 즉시 삭제 완료: ${category.name}', tag: _tag);
    });
  }

  /// 행사의 특정 필드만 Firebase에 업로드 (선택적 업데이트)
  Future<void> uploadEventFields(int eventId, Map<String, dynamic> fields) async {
    return await executeWithAuthErrorHandling('행사 필드 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 또는 플랜에서 지원하지 않는 경우 업로드 건너뛰기
      final canUseRealtimeSync = await _canUseRealtimeSync;
      if (!_isRealtimeSyncEnabled || !canUseRealtimeSync) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 행사 필드 업로드를 건너뜁니다: ${fields.keys.join(', ')}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('행사 필드 업로드 시작: ${fields.keys.join(', ')}', tag: _tag);

      // 변경된 필드만 포함하는 데이터 맵 생성
      final updateData = <String, dynamic>{};

      if (fields.containsKey('revenueGoalMode')) {
        final mode = fields['revenueGoalMode'] as RevenueGoalMode;
        updateData['revenueGoalMode'] = mode.name;
      }

      if (fields.containsKey('name')) {
        updateData['name'] = fields['name'] as String;
      }

      if (fields.containsKey('description')) {
        final description = fields['description'] as String?;
        if (description != null && description.isNotEmpty) {
          updateData['description'] = description;
        } else {
          updateData['description'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      if (fields.containsKey('imagePath')) {
        final imagePath = fields['imagePath'] as String?;
        if (imagePath != null && imagePath.isNotEmpty) {
          updateData['imagePath'] = imagePath;
        } else {
          updateData['imagePath'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      if (fields.containsKey('startDate')) {
        final startDate = fields['startDate'] as DateTime;
        updateData['startDate'] = startDate.toIso8601String();
      }

      if (fields.containsKey('endDate')) {
        final endDate = fields['endDate'] as DateTime;
        updateData['endDate'] = endDate.toIso8601String();
      }

      if (fields.containsKey('isActive')) {
        updateData['isActive'] = fields['isActive'] as bool;
      }

      // 항상 updatedAt 필드 추가
      updateData['updatedAt'] = DateTime.now().toIso8601String();

      // Firebase 문서 업데이트 (merge: true로 기존 데이터 유지)
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .update(updateData);

      LoggerUtils.logInfo('행사 필드 업로드 완료: ${fields.keys.join(', ')}', tag: _tag);
    });
  }

  /// 판매자의 특정 필드만 Firebase에 업로드 (선택적 업데이트)
  Future<void> uploadSellerFields(int sellerId, Map<String, dynamic> fields, {required int eventId}) async {
    return await executeWithAuthErrorHandling('판매자 필드 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 업로드 건너뛰기
      if (!_isRealtimeSyncEnabled) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 판매자 필드 업로드를 건너뜁니다: ${fields.keys.join(', ')}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('판매자 필드 업로드 시작: ${fields.keys.join(', ')}', tag: _tag);

      // 변경된 필드만 포함하는 데이터 맵 생성
      final updateData = <String, dynamic>{};

      if (fields.containsKey('name')) {
        updateData['name'] = fields['name'] as String;
      }

      if (fields.containsKey('isDefault')) {
        updateData['isDefault'] = fields['isDefault'] as bool;
      }

      // 항상 updatedAt 필드 추가
      updateData['updatedAt'] = DateTime.now().toIso8601String();

      // Firebase 문서 업데이트 (merge: true로 기존 데이터 유지)
      // 판매자는 events/{eventId}/sellers/{sellerId} 경로에 저장됨
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('sellers')
          .doc(sellerId.toString())
          .update(updateData);

      LoggerUtils.logInfo('판매자 필드 업로드 완료: ${fields.keys.join(', ')}', tag: _tag);
    });
  }

  /// 상품의 특정 필드만 Firebase에 업로드 (선택적 업데이트)
  Future<void> uploadProductFields(int productId, Map<String, dynamic> fields, {required int eventId}) async {
    return await executeWithAuthErrorHandling('상품 필드 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 업로드 건너뛰기
      if (!_isRealtimeSyncEnabled) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 상품 필드 업로드를 건너뜁니다: ${fields.keys.join(', ')}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('상품 필드 업로드 시작: ${fields.keys.join(', ')}', tag: _tag);

      // 변경된 필드만 포함하는 데이터 맵 생성
      final updateData = <String, dynamic>{};

      if (fields.containsKey('name')) {
        updateData['name'] = fields['name'] as String;
      }

      if (fields.containsKey('price')) {
        updateData['price'] = fields['price'] as int;
      }

      if (fields.containsKey('quantity')) {
        updateData['quantity'] = fields['quantity'] as int;
      }

      if (fields.containsKey('sellerName')) {
        final sellerName = fields['sellerName'] as String?;
        if (sellerName != null && sellerName.isNotEmpty) {
          updateData['sellerName'] = sellerName;
        } else {
          updateData['sellerName'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      if (fields.containsKey('imagePath')) {
        final imagePath = fields['imagePath'] as String?;
        if (imagePath != null && imagePath.isNotEmpty) {
          updateData['imagePath'] = imagePath;
        } else {
          updateData['imagePath'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      if (fields.containsKey('isActive')) {
        updateData['isActive'] = fields['isActive'] as bool;
      }

      if (fields.containsKey('categoryId')) {
        updateData['categoryId'] = fields['categoryId'] as int;
      }

      // 항상 updatedAt 필드 추가
      updateData['updatedAt'] = DateTime.now().toIso8601String();

      // Firebase 문서 업데이트 (merge: true로 기존 데이터 유지)
      // 상품은 events/{eventId}/products/{productId} 경로에 저장됨
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('products')
          .doc(productId.toString())
          .update(updateData);

      LoggerUtils.logInfo('상품 필드 업로드 완료: ${fields.keys.join(', ')}', tag: _tag);
    });
  }

  /// 선입금의 특정 필드만 Firebase에 업로드 (선택적 업데이트)
  Future<void> uploadPrepaymentFields(int prepaymentId, Map<String, dynamic> fields, {required int eventId}) async {
    return await executeWithAuthErrorHandling('선입금 필드 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 업로드 건너뛰기
      if (!_isRealtimeSyncEnabled) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 선입금 필드 업로드를 건너뜁니다: ${fields.keys.join(', ')}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('선입금 필드 업로드 시작: ${fields.keys.join(', ')}', tag: _tag);

      // 변경된 필드만 포함하는 데이터 맵 생성
      final updateData = <String, dynamic>{};

      if (fields.containsKey('buyerName')) {
        updateData['buyerName'] = fields['buyerName'] as String;
      }

      if (fields.containsKey('buyerContact')) {
        updateData['buyerContact'] = fields['buyerContact'] as String;
      }

      if (fields.containsKey('amount')) {
        updateData['amount'] = fields['amount'] as int;
      }

      if (fields.containsKey('pickupDays')) {
        updateData['pickupDays'] = fields['pickupDays'] as List<String>;
      }

      if (fields.containsKey('memo')) {
        final memo = fields['memo'] as String?;
        if (memo != null && memo.isNotEmpty) {
          updateData['memo'] = memo;
        } else {
          updateData['memo'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      if (fields.containsKey('isReceived')) {
        updateData['isReceived'] = fields['isReceived'] as bool;
      }

      if (fields.containsKey('bankName')) {
        updateData['bankName'] = fields['bankName'] as String;
      }

      if (fields.containsKey('email')) {
        updateData['email'] = fields['email'] as String;
      }

      if (fields.containsKey('twitterAccount')) {
        final twitterAccount = fields['twitterAccount'] as String?;
        if (twitterAccount != null && twitterAccount.isNotEmpty) {
          updateData['twitterAccount'] = twitterAccount;
        } else {
          updateData['twitterAccount'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      if (fields.containsKey('orderNumber')) {
        final orderNumber = fields['orderNumber'] as String?;
        if (orderNumber != null && orderNumber.isNotEmpty) {
          updateData['orderNumber'] = orderNumber;
        } else {
          updateData['orderNumber'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      // 항상 updatedAt 필드 추가
      updateData['updatedAt'] = DateTime.now().toIso8601String();

      // Firebase 문서 업데이트 (merge: true로 기존 데이터 유지)
      // 선입금은 events/{eventId}/prepayments/{prepaymentId} 경로에 저장됨
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayments')
          .doc(prepaymentId.toString())
          .update(updateData);

      LoggerUtils.logInfo('선입금 필드 업로드 완료: ${fields.keys.join(', ')}', tag: _tag);
    });
  }

  /// 판매 기록의 특정 필드만 Firebase에 업로드 (선택적 업데이트)
  Future<void> uploadSalesLogFields(int salesLogId, Map<String, dynamic> fields, {required int eventId}) async {
    return await executeWithAuthErrorHandling('판매 기록 필드 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 업로드 건너뛰기
      if (!_isRealtimeSyncEnabled) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 판매 기록 필드 업로드를 건너뜁니다: ${fields.keys.join(', ')}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('판매 기록 필드 업로드 시작: ${fields.keys.join(', ')}', tag: _tag);

      // 변경된 필드만 포함하는 데이터 맵 생성
      final updateData = <String, dynamic>{};

      if (fields.containsKey('productName')) {
        updateData['productName'] = fields['productName'] as String;
      }

      if (fields.containsKey('sellerName')) {
        final sellerName = fields['sellerName'] as String?;
        if (sellerName != null && sellerName.isNotEmpty) {
          updateData['sellerName'] = sellerName;
        } else {
          updateData['sellerName'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      if (fields.containsKey('soldPrice')) {
        updateData['soldPrice'] = fields['soldPrice'] as int;
      }

      if (fields.containsKey('soldQuantity')) {
        updateData['soldQuantity'] = fields['soldQuantity'] as int;
      }

      if (fields.containsKey('totalAmount')) {
        updateData['totalAmount'] = fields['totalAmount'] as int;
      }

      if (fields.containsKey('transactionType')) {
        final transactionType = fields['transactionType'] as TransactionType;
        updateData['transactionType'] = transactionType.value;
      }

      if (fields.containsKey('setDiscountAmount')) {
        updateData['setDiscountAmount'] = fields['setDiscountAmount'] as int;
      }

      if (fields.containsKey('setDiscountNames')) {
        final setDiscountNames = fields['setDiscountNames'] as String?;
        if (setDiscountNames != null && setDiscountNames.isNotEmpty) {
          updateData['setDiscountNames'] = setDiscountNames;
        } else {
          updateData['setDiscountNames'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      if (fields.containsKey('manualDiscountAmount')) {
        updateData['manualDiscountAmount'] = fields['manualDiscountAmount'] as int;
      }

      if (fields.containsKey('paymentMethod')) {
        final paymentMethod = fields['paymentMethod'] as String?;
        if (paymentMethod != null && paymentMethod.isNotEmpty) {
          updateData['paymentMethod'] = paymentMethod;
        } else {
          updateData['paymentMethod'] = FieldValue.delete(); // null 값은 필드 삭제
        }
      }

      // 항상 updatedAt 필드 추가
      updateData['updatedAt'] = DateTime.now().toIso8601String();

      // Firebase 문서 업데이트 (merge: true로 기존 데이터 유지)
      // 판매 기록은 events/{eventId}/sales_logs/{salesLogId} 경로에 저장됨
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('sales_logs')
          .doc(salesLogId.toString())
          .update(updateData);

      LoggerUtils.logInfo('판매 기록 필드 업로드 완료: ${fields.keys.join(', ')}', tag: _tag);
    });
  }

  /// 개별 선입금을 즉시 Firebase에 업로드 (실시간 동기화용)
  Future<void> uploadSinglePrepayment(Prepayment prepayment) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 또는 플랜에서 지원하지 않는 경우 업로드 건너뛰기
      final canUseRealtimeSync = await _canUseRealtimeSync;
      if (!_isRealtimeSyncEnabled || !canUseRealtimeSync) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 선입금 업로드를 건너뜁니다: ID ${prepayment.id}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('개별 선입금 즉시 업로드 시작: ID ${prepayment.id}', tag: _tag);

      final prepaymentData = prepayment.toJson();
      prepaymentData.remove('id'); // Firestore 문서 ID로 사용할 것이므로 제거

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(prepayment.eventId.toString())
          .collection('prepayments')
          .doc(prepayment.id.toString())
          .set(prepaymentData);

      LoggerUtils.logInfo('개별 선입금 즉시 업로드 완료: ID ${prepayment.id}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 선입금 즉시 업로드 실패: ID ${prepayment.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 개별 판매자를 즉시 Firebase에 업로드 (실시간 동기화용)
  /// 성능 최적화: 여러 판매자가 동시에 업로드되는 경우 배치 처리
  Future<void> uploadSingleSeller(Seller seller) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 또는 플랜에서 지원하지 않는 경우 업로드 건너뛰기
      final canUseRealtimeSync = await _canUseRealtimeSync;
      if (!_isRealtimeSyncEnabled || !canUseRealtimeSync) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 판매자 업로드를 건너뜁니다: ${seller.name}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('개별 판매자 즉시 업로드 시작: ${seller.name}', tag: _tag);

      // 배치 처리를 위한 단일 아이템 배치 사용 (향후 확장 가능)
      await uploadSellersBatch([seller]);

      LoggerUtils.logInfo('개별 판매자 즉시 업로드 완료: ${seller.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 판매자 즉시 업로드 실패: ${seller.name}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 배치 판매자 업로드 (성능 최적화)
  Future<void> uploadSellersBatch(List<Seller> sellers) async {
    if (sellers.isEmpty) return;

    return await executeWithAuthErrorHandling('배치 판매자 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('배치 판매자 업로드 시작: ${sellers.length}개', tag: _tag);

      final batch = _firestore.batch();

      for (final seller in sellers) {
        final sellerData = seller.toJson();
        sellerData.remove('id');

        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(seller.eventId.toString())
            .collection('sellers')
            .doc(seller.id.toString());

        batch.set(docRef, sellerData);
      }

      await batch.commit();
      LoggerUtils.logInfo('배치 판매자 업로드 완료: ${sellers.length}개', tag: _tag);
    });
  }

  /// 개별 목표 수익을 즉시 Firebase에 업로드 (실시간 동기화용)
  Future<void> uploadSingleRevenueGoal(RevenueGoal goal) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 또는 플랜에서 지원하지 않는 경우 업로드 건너뛰기
      final canUseRealtimeSync = await _canUseRealtimeSync;
      if (!_isRealtimeSyncEnabled || !canUseRealtimeSync) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 목표 수익 업로드를 건너뜁니다: ID ${goal.id}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('개별 목표 수익 즉시 업로드 시작: ID ${goal.id}', tag: _tag);

      final goalData = goal.toJson();
      goalData.remove('id'); // Firestore 문서 ID로 사용할 것이므로 제거

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(goal.eventId.toString())
          .collection('revenue_goals')
          .doc(goal.id.toString())
          .set(goalData);

      LoggerUtils.logInfo('개별 목표 수익 즉시 업로드 완료: ID ${goal.id}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 목표 수익 즉시 업로드 실패: ID ${goal.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 개별 세트 할인을 즉시 Firebase에 업로드 (실시간 동기화용)
  Future<void> uploadSingleSetDiscount(SetDiscount setDiscount) async {
    return await _withSyncLock('set_discounts_${setDiscount.eventId}', () async {
      return await executeWithAuthErrorHandling('세트 할인 업로드', () async {
        if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

        // 실시간 동기화가 비활성화된 경우 업로드 건너뛰기
        if (!_isRealtimeSyncEnabled) {
          LoggerUtils.logInfo('실시간 동기화가 비활성화되어 세트 할인 업로드를 건너뜁니다: ${setDiscount.name}', tag: _tag);
          return;
        }

        LoggerUtils.logInfo('개별 세트 할인 업로드 시작: ${setDiscount.name}', tag: _tag);

        // Firebase용 데이터 수동 준비 (중첩 객체 직렬화 보장)
        final setDiscountData = <String, dynamic>{
          'name': setDiscount.name,
          'discountAmount': setDiscount.discountAmount,
          'conditionType': setDiscount.conditionType.toString(),
          'productIds': setDiscount.productIds,
          'minimumAmount': setDiscount.minimumAmount,
          'categoryCondition': setDiscount.categoryCondition?.toJson(),
          'productGroupCondition': setDiscount.productGroupCondition?.toJson(),
          'allowMultipleApplications': setDiscount.allowMultipleApplications,
          'isActive': setDiscount.isActive,
          'createdAt': setDiscount.createdAt.toIso8601String(),
          'updatedAt': setDiscount.updatedAt?.toIso8601String(),
          'eventId': setDiscount.eventId,
        };

        await _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(setDiscount.eventId.toString())
            .collection('set_discounts')
            .doc(setDiscount.id.toString())
            .set(setDiscountData);

        LoggerUtils.logInfo('세트 할인 데이터 업로드 완료: ${setDiscount.name}', tag: _tag);
      });
    });
  }

  /// 배치 상품 업로드 (판매 처리 최적화용)
  Future<void> uploadProductsBatch(List<Product> products) async {
    if (products.isEmpty) return;

    return await executeWithAuthErrorHandling('배치 상품 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('배치 상품 업로드 시작: ${products.length}개', tag: _tag);

      final batch = _firestore.batch();

      for (final product in products) {
        final productData = product.toJson();
        productData.remove('id');

        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(product.eventId.toString())
            .collection('products')
            .doc(product.id.toString());

        batch.set(docRef, productData);
      }

      await batch.commit();
      LoggerUtils.logInfo('배치 상품 업로드 완료: ${products.length}개', tag: _tag);
    });
  }

  /// 배치 판매 로그 업로드 (판매 처리 최적화용)
  Future<void> uploadSalesLogsBatch(List<SalesLog> salesLogs) async {
    if (salesLogs.isEmpty) return;

    return await executeWithAuthErrorHandling('배치 판매 로그 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('배치 판매 로그 업로드 시작: ${salesLogs.length}개', tag: _tag);

      final batch = _firestore.batch();

      for (final salesLog in salesLogs) {
        final salesLogData = salesLog.toJson();
        salesLogData.remove('id');

        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(salesLog.eventId.toString())
            .collection('sales_logs')
            .doc(salesLog.id.toString());

        batch.set(docRef, salesLogData);
      }

      await batch.commit();
      LoggerUtils.logInfo('배치 판매 로그 업로드 완료: ${salesLogs.length}개', tag: _tag);
    });
  }

  /// 개별 세트 할인을 즉시 Firebase에서 삭제 (실시간 동기화용)
  Future<void> deleteSingleSetDiscount(SetDiscount setDiscount) async {
    return await _withSyncLock('set_discounts_${setDiscount.eventId}', () async {
      return await executeWithAuthErrorHandling('세트 할인 삭제', () async {
        if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

        LoggerUtils.logInfo('개별 세트 할인 즉시 삭제 시작: ${setDiscount.name}', tag: _tag);

        await _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(setDiscount.eventId.toString())
            .collection('set_discounts')
            .doc(setDiscount.id.toString())
            .delete();

        LoggerUtils.logInfo('개별 세트 할인 즉시 삭제 완료: ${setDiscount.name}', tag: _tag);
      });
    });
  }

  /// 개별 목표 수익을 즉시 Firebase에서 삭제 (실시간 동기화용)
  Future<void> deleteSingleRevenueGoal(String goalId) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('개별 목표 수익 즉시 삭제 시작: ID $goalId', tag: _tag);

      // 모든 행사에서 해당 ID의 목표 수익을 찾아서 삭제
      final eventsSnapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .get();

      for (final eventDoc in eventsSnapshot.docs) {
        try {
          await _firestore
              .collection('users')
              .doc(_currentUserId!)
              .collection('events')
              .doc(eventDoc.id)
              .collection('revenue_goals')
              .doc(goalId)
              .delete();
        } catch (e) {
          // 해당 행사에 목표 수익이 없을 수 있으므로 무시
        }
      }

      LoggerUtils.logInfo('개별 목표 수익 즉시 삭제 완료: ID $goalId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 목표 수익 즉시 삭제 실패: ID $goalId', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 개별 상품을 즉시 Firebase에서 삭제 (실시간 동기화용)
  Future<void> deleteSingleProduct(Product product) async {
    return await executeWithAuthErrorHandling('상품 삭제', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 상품 ID와 eventId 유효성 검사
      if (product.id == null) {
        throw Exception('상품 ID가 null입니다. 삭제할 수 없습니다.');
      }
      if (product.eventId <= 0) {
        throw Exception('유효하지 않은 행사 ID입니다: ${product.eventId}');
      }

      LoggerUtils.logInfo('개별 상품 즉시 삭제 시작: ${product.name} (ID: ${product.id}, EventID: ${product.eventId})', tag: _tag);

      // Firebase 문서 경로 확인 및 삭제
      final productDocRef = _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(product.eventId.toString())
          .collection('products')
          .doc(product.id.toString());

      // 문서 존재 여부 확인 (선택적)
      final docSnapshot = await productDocRef.get();
      if (!docSnapshot.exists) {
        LoggerUtils.logWarning('삭제하려는 상품이 Firebase에 존재하지 않습니다: ${product.name} (ID: ${product.id})', tag: _tag);
        // 문서가 없어도 삭제 성공으로 처리 (이미 삭제된 상태)
        return;
      }

      // 상품 이미지가 있는 경우 Firebase Storage에서도 삭제
      if (docSnapshot.exists) {
        final productData = docSnapshot.data();
        final imagePath = productData?['imagePath'] as String?;

        if (imagePath != null && imagePath.startsWith('http')) {
          try {
            // Firebase Storage URL에서 파일 경로 추출하여 삭제
            final uri = Uri.parse(imagePath);
            final pathSegments = uri.pathSegments;
            if (pathSegments.length >= 2) {
              final filePath = pathSegments.sublist(1).join('/').split('?').first;
              await FirebaseUploadUtils.deleteFile(filePath);
              LoggerUtils.logInfo('상품 이미지 Storage 삭제 완료: $filePath', tag: _tag);
            }
          } catch (e) {
            LoggerUtils.logError('상품 이미지 Storage 삭제 실패: $imagePath', error: e, tag: _tag);
            // 이미지 삭제 실패는 치명적이지 않으므로 계속 진행
          }
        }
      }

      // 실제 Firestore 문서 삭제 수행
      await productDocRef.delete();

      LoggerUtils.logInfo('개별 상품 즉시 삭제 완료: ${product.name} (ID: ${product.id})', tag: _tag);
    });
  }

  /// 여러 상품을 배치로 Firebase에서 삭제 (서버 사용량 최적화)
  Future<Map<String, dynamic>> deleteBatchProducts(List<Product> products) async {
    return await executeWithAuthErrorHandling('상품 배치 삭제', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');
      if (products.isEmpty) return {'success': 0, 'failed': 0, 'errors': []};

      LoggerUtils.logInfo('상품 배치 삭제 시작: ${products.length}개', tag: _tag);

      final batch = _firestore.batch();
      final results = <String, dynamic>{
        'success': 0,
        'failed': 0,
        'errors': <String>[],
      };

      for (final product in products) {
        try {
          // 상품 ID와 eventId 유효성 검사
          if (product.id == null) {
            results['failed']++;
            results['errors'].add('${product.name}: 상품 ID가 null입니다');
            continue;
          }
          if (product.eventId <= 0) {
            results['failed']++;
            results['errors'].add('${product.name}: 유효하지 않은 행사 ID입니다');
            continue;
          }

          // Firebase 문서 경로
          final productDocRef = _firestore
              .collection('users')
              .doc(_currentUserId!)
              .collection('events')
              .doc(product.eventId.toString())
              .collection('products')
              .doc(product.id.toString());

          // 배치에 삭제 작업 추가
          batch.delete(productDocRef);
          results['success']++;

        } catch (e) {
          results['failed']++;
          results['errors'].add('${product.name}: ${e.toString()}');
          LoggerUtils.logError('배치 삭제 준비 실패: ${product.name}', error: e, tag: _tag);
        }
      }

      // 배치 실행
      if (results['success'] > 0) {
        try {
          await batch.commit();
          LoggerUtils.logInfo('상품 배치 삭제 완료: 성공 ${results['success']}개, 실패 ${results['failed']}개', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('배치 삭제 실행 실패', error: e, tag: _tag);
          // 배치 실행 실패 시 모든 성공 카운트를 실패로 변경
          results['failed'] = results['success'] + results['failed'];
          results['success'] = 0;
          results['errors'].add('배치 삭제 실행 실패: ${e.toString()}');
        }
      }

      return results;
    });
  }

  /// 개별 선입금을 즉시 Firebase에서 삭제 (실시간 동기화용)
  Future<void> deleteSinglePrepayment(Prepayment prepayment) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('개별 선입금 즉시 삭제 시작: ID ${prepayment.id}', tag: _tag);

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(prepayment.eventId.toString())
          .collection('prepayments')
          .doc(prepayment.id.toString())
          .delete();

      LoggerUtils.logInfo('개별 선입금 즉시 삭제 완료: ID ${prepayment.id}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 선입금 즉시 삭제 실패: ID ${prepayment.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 개별 판매자를 즉시 Firebase에서 삭제 (실시간 동기화용)
  Future<void> deleteSingleSeller(Seller seller) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('개별 판매자 즉시 삭제 시작: ${seller.name}', tag: _tag);

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(seller.eventId.toString())
          .collection('sellers')
          .doc(seller.id.toString())
          .delete();

      LoggerUtils.logInfo('개별 판매자 즉시 삭제 완료: ${seller.name}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 판매자 즉시 삭제 실패: ${seller.name}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 개별 판매 기록을 즉시 Firebase에 업로드 (실시간 동기화용)
  Future<void> uploadSingleSalesLog(SalesLog salesLog) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 또는 플랜에서 지원하지 않는 경우 업로드 건너뛰기
      final canUseRealtimeSync = await _canUseRealtimeSync;
      if (!_isRealtimeSyncEnabled || !canUseRealtimeSync) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 판매 기록 업로드를 건너뜁니다: ID ${salesLog.id}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('개별 판매 기록 즉시 업로드 시작: ID ${salesLog.id}', tag: _tag);

      final salesLogData = salesLog.toJson();
      salesLogData.remove('id'); // Firestore 문서 ID로 사용할 것이므로 제거

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(salesLog.eventId.toString())
          .collection('sales_logs')
          .doc(salesLog.id.toString())
          .set(salesLogData);

      LoggerUtils.logInfo('개별 판매 기록 즉시 업로드 완료: ID ${salesLog.id}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 판매 기록 즉시 업로드 실패: ID ${salesLog.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 개별 세트 할인 거래를 즉시 Firebase에 업로드 (실시간 동기화용)
  Future<void> uploadSingleSetDiscountTransaction(SetDiscountTransaction transaction) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 업로드 건너뛰기
      if (!_isRealtimeSyncEnabled) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 세트 할인 거래 업로드를 건너뜁니다: ID ${transaction.id}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('개별 세트 할인 거래 즉시 업로드 시작: ID ${transaction.id}', tag: _tag);

      final transactionData = transaction.toFirebaseJson();

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(transaction.eventId.toString())
          .collection('set_discount_transactions')
          .doc(transaction.id.toString())
          .set(transactionData);

      LoggerUtils.logInfo('개별 세트 할인 거래 즉시 업로드 완료: ID ${transaction.id}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 세트 할인 거래 즉시 업로드 실패: ID ${transaction.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// Firebase에서 최신 상품 목록을 다운로드하여 로컬 DB와 동기화 (실시간 동기화용)
  Future<void> downloadProductsFromFirebase(
    int eventId, {
    Function(String message, double progress)? onProgress,
  }) async {
    return await executeWithAuthErrorHandling('상품 목록 다운로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('Firebase에서 최신 상품 목록 다운로드 시작: eventId $eventId', tag: _tag);
      await _downloadProducts(eventId, onProgress: onProgress);
      LoggerUtils.logInfo('Firebase에서 최신 상품 목록 다운로드 완료: eventId $eventId', tag: _tag);
    });
  }

  /// Firebase에서 최신 판매기록 목록을 다운로드하여 로컬 DB와 동기화 (실시간 동기화용)
  Future<void> downloadSalesLogsFromFirebase(int eventId) async {
    return await executeWithAuthErrorHandling('판매기록 목록 다운로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('Firebase에서 최신 판매기록 목록 다운로드 시작: eventId $eventId', tag: _tag);
      await _downloadSalesLogs(eventId);
      LoggerUtils.logInfo('Firebase에서 최신 판매기록 목록 다운로드 완료: eventId $eventId', tag: _tag);
    });
  }

  /// 개별 판매 기록을 즉시 Firebase에서 삭제 (실시간 동기화용)
  Future<void> deleteSingleSalesLog(SalesLog salesLog) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('개별 판매 기록 즉시 삭제 시작: ID ${salesLog.id}', tag: _tag);

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(salesLog.eventId.toString())
          .collection('sales_logs')
          .doc(salesLog.id.toString())
          .delete();

      LoggerUtils.logInfo('개별 판매 기록 즉시 삭제 완료: ID ${salesLog.id}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 판매 기록 즉시 삭제 실패: ID ${salesLog.id}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 개별 행사를 즉시 Firebase에 업로드 (실시간 동기화용)
  Future<void> uploadSingleEvent(Event event) async {
    return await executeWithAuthErrorHandling('행사 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 행사 생성은 모든 플랜에서 가능 (권한 체크를 위해 필요)
      LoggerUtils.logInfo('행사 업로드 시작: ${event.name}', tag: _tag);

      LoggerUtils.logInfo('개별 행사 즉시 업로드 시작: ${event.name}', tag: _tag);

      // 1단계: 행사 데이터부터 즉시 업로드 (이미지 없이)
      Event eventToUpload = event;
      String? localImagePath;
      
      if (event.imagePath != null && 
          event.imagePath!.isNotEmpty && 
          ImageSyncUtils.isLocalImagePath(event.imagePath!)) {
        // 로컬 이미지 경로 저장하고 일단 null로 설정
        localImagePath = event.imagePath!;
        eventToUpload = event.copyWith(imagePath: null);
      }

      // 행사 데이터를 Firestore에 즉시 업로드
      final eventData = eventToUpload.toFirebaseMap();

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(event.id.toString())
          .set(eventData);

      LoggerUtils.logInfo('행사 데이터 즉시 업로드 완료: ${event.name}', tag: _tag);

      // 2단계: 이미지가 있다면 백그라운드 큐에 추가
      if (localImagePath != null) {
        _addEventImageUploadTask(event, localImagePath);
        LoggerUtils.logInfo('행사 이미지 업로드를 백그라운드 큐에 추가: ${event.name}', tag: _tag);
      }
    });
  }

  /// 개별 행사를 즉시 Firebase에서 삭제 (실시간 동기화용)
  Future<void> deleteSingleEvent(Event event) async {
    return await executeWithAuthErrorHandling('행사 삭제', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('개별 행사 즉시 삭제 시작: ${event.name}', tag: _tag);

      // 1. Firestore 문서 삭제
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(event.id.toString())
          .delete();

      // 2. Firebase Storage 행사 폴더 삭제
      try {
        final storageDeleted = await FirebaseUploadUtils.deleteEventStorageFolder(_currentUserId!, event.id!);
        if (storageDeleted) {
          LoggerUtils.logInfo('행사 Storage 폴더 삭제 완료: ${event.name}', tag: _tag);
        } else {
          LoggerUtils.logWarning('행사 Storage 폴더 삭제 실패: ${event.name}', tag: _tag);
        }
      } catch (e) {
        LoggerUtils.logError('행사 Storage 폴더 삭제 중 오류: ${event.name}', tag: _tag, error: e);
        // Storage 삭제 실패해도 전체 삭제 과정은 계속 진행
      }

      // 3. 로컬 이미지 캐시 정리
      try {
        await ImageSyncUtils.clearEventImageCache(event.id!);
        LoggerUtils.logInfo('행사 로컬 이미지 캐시 정리 완료: ${event.name}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('행사 로컬 이미지 캐시 정리 실패: ${event.name}', tag: _tag, error: e);
      }

      LoggerUtils.logInfo('개별 행사 즉시 삭제 완료: ${event.name}', tag: _tag);
    });
  }
  Future<void> uploadAllData({
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 프로 플랜만 클라우드 동기화 지원
      final canUseCloudSync = await _canUseRealtimeSync;
      if (!canUseCloudSync) {
        LoggerUtils.logInfo('프로 플랜이 아니므로 데이터 업로드를 건너뜁니다', tag: _tag);
        onProgress?.call('프로 플랜에서만 클라우드 동기화를 지원합니다.');
        return;
      }

      LoggerUtils.logInfo('전체 데이터 업로드 시작', tag: _tag);
      onProgress?.call('업로드를 시작합니다...');

      // 1. 행사 데이터 업로드
      onProgress?.call('행사 데이터를 업로드하는 중...');
      await _uploadEvents();

      // 2. 체크리스트 템플릿 업로드 (전역 데이터)
      onProgress?.call('체크리스트 템플릿을 업로드하는 중...');
      await _uploadChecklistTemplates();

      // 3. 각 행사별 데이터 업로드
      final events = await _eventRepository.getAllEvents();
      if (events.isEmpty) {
        onProgress?.call('업로드할 행사 데이터가 없습니다.');
        return;
      }

      // 병렬 처리로 성능 최적화
      final totalTasks = events.length * 9; // 각 행사당 9개 작업 (체크리스트 아이템 추가)
      var completedTasks = 0;
      
      for (final event in events.where((event) => event.id != null)) {
          try {
            // 각 행사의 데이터 업로드 작업들을 순차적으로 실행하여 진행률 추적
            final tasks = [
              () => _uploadCategories(event.id!),
              () => _uploadProducts(event.id!),
              () => _uploadSellers(event.id!),
              () => _uploadPrepayments(event.id!),
              () => _uploadSalesLogs(event.id!),
              () => _uploadVirtualProducts(event.id!),
              () => _uploadProductLinks(event.id!),
              () => _uploadRevenueGoals(event.id!),
              () => _uploadChecklistItems(event.id!),
            ];

            for (int i = 0; i < tasks.length; i++) {
              await tasks[i]();
              completedTasks++;
              final progressPercent = (completedTasks / totalTasks * 100).toInt();
              final taskNames = ['카테고리', '상품', '판매자', '선입금', '판매로그', '가상상품', '상품링크', '목표수익', '체크리스트'];
              onProgress?.call('${event.name} ${taskNames[i]} 업로드 중... ($progressPercent%)');
            }
          } catch (e) {
            LoggerUtils.logWarning('행사 ${event.name} 데이터 업로드 중 일부 실패', tag: _tag, error: e);
            // 실패한 작업들도 completedTasks에 반영하여 진행률 계속 진행
            completedTasks += 6 - (completedTasks % 6);
          }
      }

      onProgress?.call('업로드가 완료되었습니다!');
      LoggerUtils.logInfo('전체 데이터 업로드 완료', tag: _tag);
    } catch (e) {
      final errorMsg = '데이터 업로드 실패: $e';
      LoggerUtils.logError(errorMsg, tag: _tag, error: e);
      onError?.call(errorMsg);
      rethrow;
    }
  }

  /// 재고현황(상품) 데이터만 업로드합니다
  Future<void> uploadInventoryData({
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');
      
      LoggerUtils.logInfo('재고현황 데이터 업로드 시작', tag: _tag);
      onProgress?.call('재고현황 데이터를 업로드하는 중...');
      
      final events = await _eventRepository.getAllEvents();
      for (final event in events) {
        if (event.id == null) continue;
        await _uploadProducts(event.id!); // 이미지 포함 업로드
      }
      
      onProgress?.call('재고현황 업로드가 완료되었습니다!');
      LoggerUtils.logInfo('재고현황 데이터 업로드 완료', tag: _tag);
    } catch (e) {
      final errorMsg = '재고현황 업로드 실패: $e';
      LoggerUtils.logError(errorMsg, tag: _tag, error: e);
      onError?.call(errorMsg);
      rethrow;
    }
  }

  /// 선입금 데이터만 업로드합니다
  Future<void> uploadPrepaymentData({
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');
      
      LoggerUtils.logInfo('선입금 데이터 업로드 시작', tag: _tag);
      onProgress?.call('선입금 데이터를 업로드하는 중...');
      
      final events = await _eventRepository.getAllEvents();
      for (final event in events) {
        if (event.id == null) continue;
        await _uploadPrepayments(event.id!);
      }
      
      onProgress?.call('선입금 업로드가 완료되었습니다!');
      LoggerUtils.logInfo('선입금 데이터 업로드 완료', tag: _tag);
    } catch (e) {
      final errorMsg = '선입금 업로드 실패: $e';
      LoggerUtils.logError(errorMsg, tag: _tag, error: e);
      onError?.call(errorMsg);
      rethrow;
    }
  }

  /// 서버에서 전체 데이터를 다운로드합니다
  Future<void> downloadAllData({
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 프로 플랜만 클라우드 동기화 지원
      final canUseCloudSync = await _canUseRealtimeSync;
      if (!canUseCloudSync) {
        LoggerUtils.logInfo('프로 플랜이 아니므로 데이터 다운로드를 건너뜁니다', tag: _tag);
        onProgress?.call('프로 플랜에서만 클라우드 동기화를 지원합니다.');
        return;
      }

      LoggerUtils.logInfo('전체 데이터 다운로드 시작', tag: _tag);
      onProgress?.call('다운로드를 시작합니다...');

      // 1. 행사 데이터 다운로드
      onProgress?.call('행사 데이터를 다운로드하는 중...');
      await _downloadEvents();

      // 2. 체크리스트 템플릿 다운로드 (전역 데이터)
      onProgress?.call('체크리스트 템플릿을 다운로드하는 중...');
      await _downloadChecklistTemplates();

      // 3. 각 행사별 데이터 다운로드
      final serverEvents = await getServerEvents();
      if (serverEvents.isEmpty) {
        onProgress?.call('서버에 행사 데이터가 없습니다.');
        return;
      }

      // 병렬 처리로 성능 최적화
      final totalTasks = serverEvents.length * 11; // 각 행사당 11개 작업 (세트 할인 거래 추가)
      var completedTasks = 0;

      for (final event in serverEvents.where((event) => event.id != null)) {
          try {
            // 각 행사의 데이터 다운로드 작업들을 순차적으로 실행하여 진행률 추적
            final tasks = [
              () => _downloadCategories(event.id!),
              () => _downloadProducts(
                event.id!, 
                onProgress: (message, progress) {
                  onProgress?.call('${event.name} 이미지 $message');
                },
              ),
              () => _downloadSellers(event.id!),
              () => _downloadPrepayments(event.id!),
              () => _downloadSalesLogs(event.id!),
              () => _downloadVirtualProducts(event.id!),
              () => _downloadProductLinks(event.id!),
              () => _downloadRevenueGoals(event.id!),
              () => _downloadSetDiscounts(event.id!),
              () => _downloadSetDiscountTransactions(event.id!),
              () => _downloadChecklistItems(event.id!),
            ];

            for (int i = 0; i < tasks.length; i++) {
              await tasks[i]();
              completedTasks++;
              final progressPercent = (completedTasks / totalTasks * 100).toInt();
              final taskNames = ['카테고리', '상품', '판매자', '선입금', '판매로그', '가상상품', '상품링크', '목표수익', '세트할인', '세트거래', '체크리스트'];
              onProgress?.call('${event.name} ${taskNames[i]} 다운로드 중... ($progressPercent%)');
            }
          } catch (e) {
            LoggerUtils.logWarning('행사 ${event.name} 데이터 다운로드 중 일부 실패', tag: _tag, error: e);
            // 실패한 작업들도 completedTasks에 반영하여 진행률 계속 진행
            completedTasks += 11 - (completedTasks % 11);
          }
      }

      onProgress?.call('다운로드가 완료되었습니다!');
      LoggerUtils.logInfo('전체 데이터 다운로드 완료', tag: _tag);
    } catch (e) {
      final errorMsg = '데이터 다운로드 실패: $e';
      LoggerUtils.logError(errorMsg, tag: _tag, error: e);
      onError?.call(errorMsg);
      rethrow;
    }
  }

  /// 행사 데이터를 업로드합니다
  Future<void> _uploadEvents() async {
    try {
      final events = await _eventRepository.getAllEvents();

      // 행사 이미지 업로드를 병렬로 처리
      final imageUploadTasks = events
          .where((event) => event.id != null && event.imagePath != null && event.imagePath!.isNotEmpty)
          .map((event) => () async {
            final imageUrl = await ImageSyncUtils.uploadEventImage(event.id!, event.imagePath!);
            return MapEntry(event.id!, imageUrl);
          }).toList();

      final imageResults = await Future.wait(imageUploadTasks.map((task) => task())); // 대량 이미지 업로드
      final imageUrlMap = Map<int, String?>.fromEntries(
        imageResults.where((entry) => entry.value != null)
      );

      final batch = _firestore.batch();

      for (final event in events) {
        if (event.id == null) continue;

        // 행사 데이터 복사 (이미지 경로 업데이트를 위해)
        var eventData = event.toFirebaseMap();

        // 병렬로 업로드된 이미지 URL 사용
        if (imageUrlMap.containsKey(event.id!)) {
          final uploadedImagePath = imageUrlMap[event.id!];
          if (uploadedImagePath != null) {
            eventData['imagePath'] = uploadedImagePath;
            LoggerUtils.logInfo('행사 이미지 업로드 완료: ${event.name}', tag: _tag);
          }
        }

        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(event.id.toString());

        batch.set(docRef, eventData);
      }

      await batch.commit();
      LoggerUtils.logInfo('행사 데이터 업로드 완료: ${events.length}개', tag: _tag);

      // 사용량 추적
      await _usageTrackingService.trackFirebaseUpload('events', count: events.length);
    } catch (e) {
      LoggerUtils.logError('행사 데이터 업로드 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 카테고리 데이터를 업로드합니다
  Future<void> _uploadCategories(int eventId) async {
    try {
      // 데이터베이스에서 카테고리 데이터 직접 조회
      final categories = await _databaseService.safeTransaction((txn) async {
        return await txn.query(
          'categories',
          where: 'event_id = ?',
          whereArgs: [eventId],
        );
      }, taskName: 'getCategoriesForUpload');

      LoggerUtils.logInfo('카테고리 데이터 업로드 시작 (행사 $eventId): ${categories.length}개', tag: _tag);

      final batch = _firestore.batch();

      for (final categoryMap in categories) {
        try {
          // 카테고리 Map 데이터를 JSON 형식으로 변환
          final categoryData = Map<String, dynamic>.from(categoryMap);
          
          // ID 추가 (데이터베이스에서 가져온 것이므로)
          final categoryId = categoryData['id'];
          if (categoryId == null) continue;

          // Firestore 저장을 위해 id 제거
          categoryData.remove('id');
          
          // 카테고리명 변환 (event_id -> eventId)
          if (categoryData.containsKey('event_id')) {
            categoryData['eventId'] = categoryData.remove('event_id');
          }
          if (categoryData.containsKey('sort_order')) {
            categoryData['sortOrder'] = categoryData.remove('sort_order');
          }

          final docRef = _firestore
              .collection('users')
              .doc(_currentUserId!)
              .collection('events')
              .doc(eventId.toString())
              .collection('categories')
              .doc(categoryId.toString());

          batch.set(docRef, categoryData);
        } catch (e) {
          LoggerUtils.logWarning('카테고리 데이터 변환 실패: ${categoryMap['name']}', tag: _tag, error: e);
        }
      }

      await batch.commit();
      LoggerUtils.logInfo('카테고리 데이터 업로드 완료 (행사 $eventId): ${categories.length}개', tag: _tag);

      // 사용량 추적
      await _usageTrackingService.trackFirebaseUpload('categories', count: categories.length);
    } catch (e) {
      LoggerUtils.logError('카테고리 데이터 업로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 상품 데이터를 업로드합니다 (이미지 포함)
  Future<void> _uploadProducts(int eventId) async {
    try {
      final products = await _productRepository.getProductsByEventId(eventId);

      // 이미지 업로드를 병렬로 처리
      final imageUploadTasks = products
          .where((product) => product.imagePath != null && product.imagePath!.isNotEmpty)
          .map((product) => () async {
            final imageUrl = await ImageSyncUtils.uploadProductImageWithCategoryId(
              eventId,
              product.categoryId,
              product.name,
              product.imagePath!
            );
            return MapEntry(product.id!, imageUrl);
          }).toList();

      final imageResults = await Future.wait(imageUploadTasks.map((task) => task())); // 상품 이미지 대량 업로드
      final imageUrlMap = Map<int, String?>.fromEntries(
        imageResults.where((entry) => entry.value != null)
      );

      final batch = _firestore.batch();

      for (final product in products) {
        if (product.id == null) continue;

        // 상품 데이터 복사 (이미지 경로 업데이트를 위해)
        var productData = product.toJson();

        // 병렬로 업로드된 이미지 URL 사용
        if (imageUrlMap.containsKey(product.id!)) {
          final uploadedImagePath = imageUrlMap[product.id!];
          if (uploadedImagePath != null) {
            productData['imagePath'] = uploadedImagePath;
            LoggerUtils.logInfo('상품 이미지 업로드 완료: ${product.name}', tag: _tag);
          }
        }

        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(eventId.toString())
            .collection('products')
            .doc(product.id.toString());

        batch.set(docRef, productData);
      }

      await batch.commit();
      LoggerUtils.logInfo('상품 데이터 업로드 완료 (행사 $eventId): ${products.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('상품 데이터 업로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매자 데이터를 업로드합니다
  Future<void> _uploadSellers(int eventId) async {
    try {
      final sellers = await _sellerRepository.getSellersByEventId(eventId);
      final batch = _firestore.batch();

      for (final seller in sellers) {
        if (seller.id == null) continue;

        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(eventId.toString())
            .collection('sellers')
            .doc(seller.id.toString());

        batch.set(docRef, seller.toJson());
      }

      await batch.commit();
      LoggerUtils.logInfo('판매자 데이터 업로드 완료 (행사 $eventId): ${sellers.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매자 데이터 업로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 데이터를 업로드합니다
  Future<void> _uploadPrepayments(int eventId) async {
    try {
      final prepayments = await _prepaymentRepository.getPrepaymentsByEventId(eventId);
      final batch = _firestore.batch();

      for (final prepayment in prepayments) {
        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(eventId.toString())
            .collection('prepayments')
            .doc(prepayment.id.toString());

        batch.set(docRef, prepayment.toJson());
      }

      await batch.commit();
      LoggerUtils.logInfo('선입금 데이터 업로드 완료 (행사 $eventId): ${prepayments.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 데이터 업로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 배치를 Firebase에 업로드합니다 (public 메서드)
  Future<void> uploadPrepaymentsBatch(List<Prepayment> prepayments) async {
    if (prepayments.isEmpty) return;

    try {
      LoggerUtils.logInfo('선입금 배치 업로드 시작: ${prepayments.length}개', tag: _tag);

      final batch = _firestore.batch();

      for (final prepayment in prepayments) {
        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(prepayment.eventId.toString())
            .collection('prepayments')
            .doc(prepayment.id.toString());

        batch.set(docRef, prepayment.toJson());
      }

      await batch.commit();
      LoggerUtils.logInfo('선입금 배치 업로드 완료: ${prepayments.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 배치 업로드 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록 데이터를 업로드합니다
  Future<void> _uploadSalesLogs(int eventId) async {
    try {
      final salesLogs = await _salesLogRepository.getSalesLogsByEventId(eventId);
      final batch = _firestore.batch();

      for (final salesLog in salesLogs) {
        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(eventId.toString())
            .collection('sales_logs')
            .doc(salesLog.id.toString());

        batch.set(docRef, salesLog.toJson());
      }

      await batch.commit();
      LoggerUtils.logInfo('판매 기록 업로드 완료 (행사 $eventId): ${salesLogs.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매 기록 업로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 가상상품 데이터를 업로드합니다
  Future<void> _uploadVirtualProducts(int eventId) async {
    try {
      final virtualProducts = await _getVirtualProductsByEventId(eventId);
      final batch = _firestore.batch();

      for (final virtualProduct in virtualProducts) {
        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(eventId.toString())
            .collection('virtual_products')
            .doc(virtualProduct.id.toString());

        batch.set(docRef, virtualProduct.toMap());
      }

      await batch.commit();
      LoggerUtils.logInfo('가상상품 데이터 업로드 완료 (행사 $eventId): ${virtualProducts.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('가상상품 데이터 업로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 연동내역 데이터를 업로드합니다
  Future<void> _uploadProductLinks(int eventId) async {
    try {
      final productLinks = await _prepaymentProductLinkRepository.getAllLinksByEventId(eventId);
      final batch = _firestore.batch();

      for (final link in productLinks) {
        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(eventId.toString())
            .collection('product_links')
            .doc('${link.virtualProductId}_${link.productId}');

        batch.set(docRef, link.toJson());
      }

      await batch.commit();
      LoggerUtils.logInfo('연동내역 데이터 업로드 완료 (행사 $eventId): ${productLinks.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('연동내역 데이터 업로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 행사 데이터를 다운로드합니다
  Future<void> _downloadEvents() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .get();

      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id);
          var event = Event.fromFirebaseMap(data);

          // 이미지가 있는 경우 Firebase Storage에서 다운로드
          if (event.imagePath != null && event.imagePath!.isNotEmpty) {
            try {
              final localImagePath = await ImageSyncUtils.downloadEventImage(
                event.id!,
                event.imagePath!
              );
              if (localImagePath != null) {
                // 로컬 이미지 경로로 업데이트
                event = event.copyWith(imagePath: localImagePath);
                LoggerUtils.logInfo('행사 이미지 다운로드 완료: ${event.name}', tag: _tag);
              }
            } catch (imageError) {
              LoggerUtils.logWarning('행사 이미지 다운로드 실패: ${event.name}', tag: _tag, error: imageError);
              // 이미지 다운로드 실패해도 행사 데이터는 저장 계속
            }
          }

          // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
          await _databaseService.safeTransaction((txn) async {
            // 기존 행사 확인
            final existing = await txn.query(
              'events',
              where: 'id = ?',
              whereArgs: [event.id!],
            );

            if (existing.isEmpty) {
              // 새로 삽입 (실시간 동기화 트리거 안함)
              await txn.insert('events', event.toMap());
              LoggerUtils.logDebug('외부 행사 로컬 저장 완료 (업로드 안함): ${event.name}', tag: _tag);
            } else {
              // 업데이트 (실시간 동기화 트리거 안함)
              await txn.update(
                'events',
                event.toMap(),
                where: 'id = ?',
                whereArgs: [event.id!],
              );
              LoggerUtils.logDebug('외부 행사 로컬 업데이트 완료 (업로드 안함): ${event.name}', tag: _tag);
            }
          }, taskName: 'downloadEvent');
        } catch (e) {
          LoggerUtils.logWarning('행사 데이터 처리 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      LoggerUtils.logInfo('행사 데이터 다운로드 완료: ${snapshot.docs.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('행사 데이터 다운로드 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 상품 데이터를 다운로드합니다 (이미지 다운로드 동시성 제어)
  Future<void> _downloadProducts(
    int eventId, {
    Function(String message, double progress)? onProgress,
  }) async {
    return await executeWithAuthErrorHandling('상품 데이터 다운로드', () async {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('products')
          .get();

      LoggerUtils.logInfo('상품 데이터 다운로드 시작 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);

      // 이미지 다운로드가 필요한 상품들과 그렇지 않은 상품들 분리
      final List<Product> productsWithImages = [];
      final List<Product> productsWithoutImages = [];

      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id);
          var product = Product.fromJson(data);

          if (product.imagePath != null && 
              product.imagePath!.isNotEmpty && 
              product.imagePath!.startsWith('http')) {
            productsWithImages.add(product);
          } else {
            productsWithoutImages.add(product);
          }
        } catch (e) {
          LoggerUtils.logWarning('상품 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      // 1. 이미지가 없는 상품들 배치로 빠르게 저장 (효율적)
      if (productsWithoutImages.isNotEmpty) {
        await _saveProductsBatchToLocal(productsWithoutImages);
      }

      // 2. 이미지가 있는 상품들은 동시성 제어하여 순차 처리
      await _downloadProductImagesWithConcurrencyControl(
        productsWithImages, 
        eventId,
        onProgress: (message, progress) {
          // 상위 콜백으로 진행률 전달 (80% ~ 95% 구간 사용)
          final adjustedProgress = 0.8 + (progress * 0.15);
          onProgress?.call(message, adjustedProgress);
        },
      );

      LoggerUtils.logInfo('상품 데이터 다운로드 완료 (행사 $eventId): 총 ${snapshot.docs.length}개', tag: _tag);
    });
  }

  /// 상품 이미지들을 배치 단위로 다운로드 (메모리 최적화)
  Future<void> _downloadProductImagesWithConcurrencyControl(
    List<Product> products,
    int eventId, {
    Function(String message, double progress)? onProgress,
  }) async {
    LoggerUtils.logInfo('이미지 다운로드 시작: ${products.length}개 (배치 단위 처리)', tag: _tag);

    // 이미지가 있고 로컬에 없는 상품만 필터링
    final productsWithImages = <Product>[];
    for (final product in products) {
      if (product.imagePath == null || product.imagePath!.isEmpty) continue;

      // 이미 로컬 이미지인 경우 건너뛰기
      if (ImageSyncUtils.isLocalImagePath(product.imagePath!)) {
        final localExists = await ImageSyncUtils.localImageExists(product.imagePath!);
        if (localExists) {
          LoggerUtils.logDebug('로컬 이미지 존재 - 다운로드 건너뛰기: ${product.name}', tag: _tag);
          continue;
        }
      }

      // 네트워크 이미지이고 로컬에 없는 경우만 다운로드 대상
      if (ImageSyncUtils.isNetworkImagePath(product.imagePath!)) {
        // 로컬에 동일한 이미지가 있는지 확인
        final localImagePath = await _getExpectedLocalImagePath(eventId, product.id!);
        final localExists = await ImageSyncUtils.localImageExists(localImagePath);

        if (!localExists) {
          productsWithImages.add(product);
        } else {
          LoggerUtils.logDebug('로컬에 이미지 존재 - 다운로드 건너뛰기: ${product.name}', tag: _tag);
          // 상품 데이터를 로컬 이미지 경로로 업데이트
          final updatedProduct = product.copyWith(imagePath: localImagePath);
          await _saveProductToLocal(updatedProduct);
        }
      }
    }

    if (productsWithImages.isEmpty) {
      LoggerUtils.logInfo('다운로드할 이미지가 없음', tag: _tag);
      return;
    }

    LoggerUtils.logInfo('다운로드 대상 이미지: ${productsWithImages.length}개', tag: _tag);

    // 진행률 콜백 호출
    onProgress?.call('이미지 다운로드 시작: ${productsWithImages.length}개', 0.0);

    // 배치 크기 설정 (메모리 사용량 고려)
    const int batchSize = 10; // 한 번에 10개씩 처리 (성능 향상)
    int processedCount = 0;

    // 배치 단위로 처리
    for (int i = 0; i < productsWithImages.length; i += batchSize) {
      final endIndex = (i + batchSize).clamp(0, productsWithImages.length);
      final batch = productsWithImages.sublist(i, endIndex);

      LoggerUtils.logInfo('배치 ${(i ~/ batchSize) + 1} 처리 중: ${batch.length}개 이미지', tag: _tag);

      // 배치 내 이미지들을 병렬로 다운로드
      final batchTasks = batch.map((product) =>
        _downloadSingleProductImageWithTimeout(product, eventId)
      ).toList();

      try {
        // 배치 완료 대기 (타임아웃: 배치당 10분)
        await Future.wait(batchTasks).timeout(
          const Duration(minutes: 10),
          onTimeout: () {
            LoggerUtils.logWarning('배치 ${(i ~/ batchSize) + 1} 타임아웃', tag: _tag);
            return <void>[];
          },
        );

        processedCount += batch.length;
        final progress = processedCount / productsWithImages.length;
        onProgress?.call('이미지 다운로드 진행: $processedCount/${productsWithImages.length}개 완료', progress);
        LoggerUtils.logInfo('배치 완료: $processedCount/${productsWithImages.length}', tag: _tag);

        // 배치 간 대기 시간 증가 (Firebase Storage 부하 분산 및 메모리 정리)
        if (i + batchSize < productsWithImages.length) {
          await Future.delayed(const Duration(seconds: 1)); // 500ms → 1초로 증가

          // 메모리 정리 강제 실행
          _checkAndCleanupImageDownloadState();
        }

      } catch (e) {
        LoggerUtils.logError('배치 ${(i ~/ batchSize) + 1} 처리 실패', tag: _tag, error: e);
        // 배치 실패해도 다음 배치 계속 진행
      }
    }

    // 최종 정리 및 결과 리포트
    _forceCleanupImageDownloadState();

    // 성공률 계산 및 로그
    final totalImages = productsWithImages.length;
    final successRate = totalImages > 0 ? (processedCount / totalImages * 100) : 100;

    onProgress?.call('모든 이미지 다운로드 완료', 1.0);
    LoggerUtils.logInfo('이미지 다운로드 완료: $processedCount/$totalImages개 처리됨 (성공률: ${successRate.toStringAsFixed(1)}%)', tag: _tag);

    if (successRate < 100) {
      LoggerUtils.logWarning('일부 이미지 다운로드 실패. 재시도 로직이 적용되었지만 완전하지 않을 수 있습니다.', tag: _tag);
    }
  }

  /// 개별 상품 이미지를 타임아웃과 함께 다운로드
  Future<void> _downloadSingleProductImageWithTimeout(Product product, int eventId) async {
    final imageUrl = product.imagePath!;

    // 다운로드 시작 표시
    _downloadingImages.add(imageUrl);
    _currentDownloads++;

    try {
      // ImageSyncUtils에서 이미 재시도 로직과 90초 타임아웃이 적용됨
      final localImagePath = await ImageSyncUtils.downloadProductImage(
        eventId,
        product.id!,
        imageUrl
      );

      Product updatedProduct = product;
      if (localImagePath != null) {
        updatedProduct = product.copyWith(imagePath: localImagePath);
        LoggerUtils.logInfo('상품 이미지 다운로드 완료: ${product.name}', tag: _tag);
      } else {
        LoggerUtils.logWarning('상품 이미지 다운로드 실패: ${product.name}', tag: _tag);
      }

      // 상품 정보를 로컬에 저장
      await _saveProductToLocal(updatedProduct);

    } catch (e) {
      LoggerUtils.logError('상품 이미지 다운로드 실패: ${product.name}', tag: _tag, error: e);
      // 이미지 다운로드 실패해도 원본 상품 데이터는 저장
      try {
        await _saveProductToLocal(product);
      } catch (saveError) {
        LoggerUtils.logError('상품 저장 실패: ${product.name}', tag: _tag, error: saveError);
      }
    } finally {
      // 다운로드 완료 표시 (반드시 실행)
      _downloadingImages.remove(imageUrl);
      if (_currentDownloads > 0) {
        _currentDownloads--;
      }
      LoggerUtils.logDebug('이미지 다운로드 정리 완료: ${product.name} (현재 다운로드: $_currentDownloads)', tag: _tag);
    }
  }

  /// 카테고리 데이터를 다운로드합니다 (배치 처리 최적화)
  Future<void> _downloadCategories(int eventId) async {
    return await executeWithAuthErrorHandling('카테고리 데이터 다운로드', () async {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('categories')
          .get();

      LoggerUtils.logInfo('카테고리 데이터 다운로드 시작 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);

      // 카테고리 데이터 파싱
      final categories = <model_category.Category>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id);
          final category = model_category.Category.fromJson(data);
          categories.add(category);
        } catch (e) {
          LoggerUtils.logWarning('카테고리 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      // 배치 처리로 한번에 저장 (효율적)
      if (categories.isNotEmpty) {
        await _saveCategoriesBatchToLocal(categories);
      }

      LoggerUtils.logInfo('카테고리 데이터 다운로드 완료 (행사 $eventId): ${categories.length}개', tag: _tag);
    });
  }

  /// 상품을 로컬 DB에 저장 (중복 제거, 업로드 방지)
  Future<void> _saveProductToLocal(Product product) async {
    try {
      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        // 기존 상품 확인
        final existing = await txn.query(
          'products',
          where: 'id = ?',
          whereArgs: [product.id!],
        );

        if (existing.isEmpty) {
          // 새로 삽입 (실시간 동기화 트리거 안함)
          await txn.insert('products', product.toMap());
          LoggerUtils.logDebug('외부 상품 로컬 저장 완료 (업로드 안함): ${product.name}', tag: _tag);
        } else {
          // 업데이트 (실시간 동기화 트리거 안함)
          await txn.update(
            'products',
            product.toMap(),
            where: 'id = ?',
            whereArgs: [product.id!],
          );
          LoggerUtils.logDebug('외부 상품 로컬 업데이트 완료 (업로드 안함): ${product.name}', tag: _tag);
        }
      }, taskName: 'saveProductToLocal');
    } catch (e) {
      LoggerUtils.logError('상품 로컬 저장 실패: ${product.name}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 상품들을 배치로 로컬 DB에 저장 (효율적인 방식)
  Future<void> _saveProductsBatchToLocal(List<Product> products) async {
    try {
      LoggerUtils.logInfo('상품 배치 저장 시작: ${products.length}개', tag: _tag);

      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        for (final product in products) {
          // 기존 상품 확인
          final existing = await txn.query(
            'products',
            where: 'id = ?',
            whereArgs: [product.id!],
          );

          if (existing.isEmpty) {
            // 새로 삽입 (실시간 동기화 트리거 안함)
            await txn.insert('products', product.toMap());
          } else {
            // 업데이트 (실시간 동기화 트리거 안함)
            await txn.update(
              'products',
              product.toMap(),
              where: 'id = ?',
              whereArgs: [product.id!],
            );
          }
        }
      }, taskName: 'saveProductsBatch');

      LoggerUtils.logInfo('상품 배치 저장 완료: ${products.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('상품 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 카테고리들을 배치로 로컬 DB에 저장 (효율적인 방식)
  Future<void> _saveCategoriesBatchToLocal(List<model_category.Category> categories) async {
    try {
      LoggerUtils.logInfo('카테고리 배치 저장 시작: ${categories.length}개', tag: _tag);

      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        for (final category in categories) {
          // 기존 카테고리 확인
          final existing = await txn.query(
            'categories',
            where: 'id = ?',
            whereArgs: [category.id!],
          );

          if (existing.isEmpty) {
            // 새로 삽입 (실시간 동기화 트리거 안함)
            await txn.insert('categories', category.toMap());
          } else {
            // 업데이트 (실시간 동기화 트리거 안함)
            await txn.update(
              'categories',
              category.toMap(),
              where: 'id = ?',
              whereArgs: [category.id!],
            );
          }
        }
      }, taskName: 'saveCategoriesBatch');

      LoggerUtils.logInfo('카테고리 배치 저장 완료: ${categories.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('카테고리 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }



  /// 판매자 데이터를 다운로드합니다 (배치 처리 최적화)
  Future<void> _downloadSellers(int eventId) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('sellers')
          .get();

      LoggerUtils.logInfo('판매자 데이터 다운로드 시작 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);

      // 판매자 데이터 파싱
      final sellers = <Seller>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id);
          final seller = Seller.fromJson(data);
          sellers.add(seller);
        } catch (e) {
          LoggerUtils.logWarning('판매자 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      // 배치 처리로 한번에 저장 (효율적)
      if (sellers.isNotEmpty) {
        await _saveSellersBatchToLocal(sellers);
      }

      LoggerUtils.logInfo('판매자 데이터 다운로드 완료 (행사 $eventId): ${sellers.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매자 데이터 다운로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매자들을 배치로 로컬 DB에 저장 (효율적인 방식)
  Future<void> _saveSellersBatchToLocal(List<Seller> sellers) async {
    try {
      LoggerUtils.logInfo('판매자 배치 저장 시작: ${sellers.length}개', tag: _tag);

      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        for (final seller in sellers) {
          // 기존 판매자 확인
          final existing = await txn.query(
            'sellers',
            where: 'id = ?',
            whereArgs: [seller.id!],
          );

          if (existing.isEmpty) {
            // 새로 삽입 (실시간 동기화 트리거 안함)
            await txn.insert('sellers', seller.toMap());
          } else {
            // 업데이트 (실시간 동기화 트리거 안함)
            await txn.update(
              'sellers',
              seller.toMap(),
              where: 'id = ?',
              whereArgs: [seller.id!],
            );
          }
        }
      }, taskName: 'saveSellersBatch');

      LoggerUtils.logInfo('판매자 배치 저장 완료: ${sellers.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매자 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금 데이터를 다운로드합니다 (배치 처리 최적화)
  Future<void> _downloadPrepayments(int eventId) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('prepayments')
          .get();

      LoggerUtils.logInfo('선입금 데이터 다운로드 시작 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);

      // 선입금 데이터 파싱
      final prepayments = <Prepayment>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id);
          final prepayment = Prepayment.fromJson(data);
          prepayments.add(prepayment);
        } catch (e) {
          LoggerUtils.logWarning('선입금 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      // 배치 처리로 한번에 저장 (효율적)
      if (prepayments.isNotEmpty) {
        await _savePrepaymentsBatchToLocal(prepayments);
      }

      LoggerUtils.logInfo('선입금 데이터 다운로드 완료 (행사 $eventId): ${prepayments.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('선입금 데이터 다운로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 선입금들을 배치로 로컬 DB에 저장 (효율적인 방식)
  Future<void> _savePrepaymentsBatchToLocal(List<Prepayment> prepayments) async {
    try {
      LoggerUtils.logInfo('선입금 배치 저장 시작: ${prepayments.length}개', tag: _tag);

      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        for (final prepayment in prepayments) {
          // 기존 선입금 확인
          final existing = await txn.query(
            'prepayments',
            where: 'id = ?',
            whereArgs: [prepayment.id],
          );

          if (existing.isEmpty) {
            // 새로 삽입 (실시간 동기화 트리거 안함)
            await txn.insert('prepayments', prepayment.toMap());
          } else {
            // 업데이트 (실시간 동기화 트리거 안함)
            await txn.update(
              'prepayments',
              prepayment.toMap(),
              where: 'id = ?',
              whereArgs: [prepayment.id],
            );
          }
        }
      }, taskName: 'savePrepaymentsBatch');

      LoggerUtils.logInfo('선입금 배치 저장 완료: ${prepayments.length}개', tag: _tag);

      // 사용량 추적
      await _usageTrackingService.trackFirebaseDownload('prepayments', count: prepayments.length);
    } catch (e) {
      LoggerUtils.logError('선입금 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록 데이터를 다운로드합니다 (배치 처리 + 페이지네이션 최적화)
  Future<void> _downloadSalesLogs(int eventId) async {
    try {
      LoggerUtils.logInfo('📊 판매 기록 배치 다운로드 시작 (행사 $eventId)', tag: _tag);

      const int batchSize = 500; // 한 번에 500개씩 처리
      var totalDownloaded = 0;
      var hasMore = true;
      DocumentSnapshot? lastDoc;

      final allSalesLogs = <SalesLog>[];

      while (hasMore) {
        Query query = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(eventId.toString())
            .collection('sales_logs')
            .orderBy('saleTimestamp', descending: true) // 최신순 정렬
            .limit(batchSize);

        // 페이지네이션 적용
        if (lastDoc != null) {
          query = query.startAfterDocument(lastDoc);
        }

        final snapshot = await query.get();

        if (snapshot.docs.isEmpty) {
          hasMore = false;
          break;
        }

        LoggerUtils.logInfo('📥 판매 기록 배치 다운로드 중: ${snapshot.docs.length}개 (총 ${totalDownloaded + snapshot.docs.length}개)', tag: _tag);

        // 판매 기록 데이터 파싱
        final batchSalesLogs = <SalesLog>[];
        for (final doc in snapshot.docs) {
          try {
            final data = doc.data() as Map<String, dynamic>;
            data['id'] = int.parse(doc.id);
            final salesLog = SalesLog.fromJson(data);
            batchSalesLogs.add(salesLog);
          } catch (e) {
            LoggerUtils.logWarning('판매 기록 파싱 실패: ${doc.id}', tag: _tag, error: e);
          }
        }

        allSalesLogs.addAll(batchSalesLogs);
        totalDownloaded += batchSalesLogs.length;
        lastDoc = snapshot.docs.last;

        // 더 가져올 데이터가 있는지 확인
        hasMore = snapshot.docs.length == batchSize;
      }

      // 모든 데이터를 한 번에 배치 저장 (DB 트랜잭션 최적화)
      if (allSalesLogs.isNotEmpty) {
        LoggerUtils.logInfo('💾 판매 기록 배치 저장 시작: ${allSalesLogs.length}개', tag: _tag);
        await _saveSalesLogsBatchToLocal(allSalesLogs);
        LoggerUtils.logInfo('✅ 판매 기록 배치 저장 완료: ${allSalesLogs.length}개', tag: _tag);
      }

      LoggerUtils.logInfo('🎉 판매 기록 배치 다운로드 완료 (행사 $eventId): ${totalDownloaded}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('❌ 판매 기록 배치 다운로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 판매 기록들을 배치로 로컬 DB에 저장 (효율적인 방식)
  Future<void> _saveSalesLogsBatchToLocal(List<SalesLog> salesLogs) async {
    try {
      LoggerUtils.logInfo('판매 기록 배치 저장 시작: ${salesLogs.length}개', tag: _tag);

      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        for (final salesLog in salesLogs) {
          // 기존 판매 기록 확인
          final existing = await txn.query(
            'sales_log',
            where: 'id = ?',
            whereArgs: [salesLog.id],
          );

          if (existing.isEmpty) {
            // 새로 삽입 (실시간 동기화 트리거 안함)
            await txn.insert('sales_log', salesLog.toMap());
          } else {
            // 업데이트 (실시간 동기화 트리거 안함)
            await txn.update(
              'sales_log',
              salesLog.toMap(),
              where: 'id = ?',
              whereArgs: [salesLog.id],
            );
          }
        }
      }, taskName: 'saveSalesLogsBatch');

      LoggerUtils.logInfo('판매 기록 배치 저장 완료: ${salesLogs.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('판매 기록 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 목표 수익 데이터를 업로드합니다
  Future<void> _uploadRevenueGoals(int eventId) async {
    try {
      final revenueGoalRepository = RevenueGoalRepository(_databaseService);
      final goals = await revenueGoalRepository.getGoalsByEventId(eventId);
      final batch = _firestore.batch();

      for (final goal in goals) {
        final docRef = _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(eventId.toString())
            .collection('revenue_goals')
            .doc(goal.id!);

        final goalData = goal.toJson();
        goalData.remove('id'); // Firestore 문서 ID로 사용할 것이므로 제거
        batch.set(docRef, goalData);
      }

      await batch.commit();
      LoggerUtils.logInfo('목표 수익 데이터 업로드 완료 (행사 $eventId): ${goals.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('목표 수익 데이터 업로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 목표 수익 데이터를 다운로드합니다
  Future<void> _downloadRevenueGoals(int eventId) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('revenue_goals')
          .get();

      final revenueGoalRepository = RevenueGoalRepository(_databaseService);

      // 기존 목표 수익 삭제
      await revenueGoalRepository.deleteGoalsByEventId(eventId);

      // 새 목표 수익 추가
      for (final doc in snapshot.docs) {
        final goalData = doc.data();
        goalData['id'] = doc.id;
        final goal = RevenueGoal.fromJson(goalData);
        await revenueGoalRepository.addGoal(goal);
      }

      LoggerUtils.logInfo('목표 수익 데이터 다운로드 완료 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('목표 수익 데이터 다운로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 세트 할인 데이터를 다운로드합니다 (동기화 보호 적용)
  Future<void> _downloadSetDiscounts(int eventId) async {
    return await _withSyncLock('set_discounts_$eventId', () async {
      try {
        LoggerUtils.logInfo('세트 할인 데이터 다운로드 시작 (행사 $eventId)', tag: _tag);

        final snapshot = await _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(eventId.toString())
            .collection('set_discounts')
            .get();

        final setDiscountRepository = SetDiscountRepository(database: _databaseService);

        // 기존 세트 할인 가져오기
        final existingSetDiscounts = await setDiscountRepository.getAllSetDiscounts(eventId: eventId);
        final existingIds = existingSetDiscounts.map((e) => e.id!).toSet();

        // Firebase에서 가져온 세트 할인 처리
        final firebaseIds = <int>{};
        final newSetDiscounts = <SetDiscount>[];

        for (final doc in snapshot.docs) {
          try {
            final setDiscountData = doc.data();
            final id = int.parse(doc.id);
            setDiscountData['id'] = id;
            final setDiscount = SetDiscount.fromJson(setDiscountData);

            firebaseIds.add(id);

            // 기존에 없는 세트 할인이거나 업데이트된 세트 할인인 경우
            final existing = existingSetDiscounts.where((e) => e.id == id).firstOrNull;
            if (existing == null || _shouldUpdateSetDiscount(existing, setDiscount)) {
              newSetDiscounts.add(setDiscount);
            }
          } catch (e) {
            LoggerUtils.logWarning('세트 할인 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
          }
        }

        // 안전하게 업데이트 (트랜잭션 없이 순차 처리)
        // Firebase에 없는 로컬 세트 할인 삭제 (동기화가 완전히 성공한 경우에만)
        // 네트워크 오류나 부분적 동기화 실패 시 로컬 데이터 보호
        if (firebaseIds.isNotEmpty || existingIds.isEmpty) {
          // Firebase에서 데이터를 성공적으로 가져왔거나, 로컬에 데이터가 없는 경우에만 삭제 수행
          for (final existingId in existingIds) {
            if (!firebaseIds.contains(existingId)) {
              // 추가 안전 검사: 최근에 생성된 로컬 데이터는 보호 (5분 이내)
              final existingDiscount = await setDiscountRepository.getSetDiscountById(existingId);
              if (existingDiscount != null) {
                final now = DateTime.now().millisecondsSinceEpoch;
                final createdTime = existingDiscount.createdAt.millisecondsSinceEpoch;
                final timeDiff = now - createdTime;

                // 5분(300,000ms) 이내에 생성된 데이터는 삭제하지 않음
                if (timeDiff > 300000) {
                  await setDiscountRepository.deleteSetDiscount(existingId);
                  LoggerUtils.logDebug('로컬 세트 할인 삭제: $existingId', tag: _tag);
                } else {
                  LoggerUtils.logDebug('최근 생성된 세트 할인 보호: $existingId (${timeDiff}ms 전 생성)', tag: _tag);
                }
              }
            }
          }
        } else {
          LoggerUtils.logWarning('Firebase 세트 할인 데이터가 비어있음 - 로컬 데이터 삭제 건너뜀', tag: _tag);
        }

        // 새로운/업데이트된 세트 할인 저장
        for (final setDiscount in newSetDiscounts) {
          if (existingIds.contains(setDiscount.id)) {
            await setDiscountRepository.updateSetDiscount(setDiscount);
            LoggerUtils.logDebug('세트 할인 업데이트: ${setDiscount.id}', tag: _tag);
          } else {
            await setDiscountRepository.insertSetDiscount(setDiscount);
            LoggerUtils.logDebug('새 세트 할인 추가: ${setDiscount.id}', tag: _tag);
          }
        }

        LoggerUtils.logInfo('세트 할인 데이터 다운로드 완료 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('세트 할인 데이터 다운로드 실패 (행사 $eventId)', tag: _tag, error: e);
        rethrow;
      }
    });
  }

  /// 세트 할인 업데이트 필요 여부 확인
  bool _shouldUpdateSetDiscount(SetDiscount existing, SetDiscount firebase) {
    // updatedAt 비교 (Firebase 데이터가 더 최신인 경우 업데이트)
    if (firebase.updatedAt != null && existing.updatedAt != null) {
      return firebase.updatedAt!.isAfter(existing.updatedAt!);
    }
    // updatedAt이 없는 경우 createdAt 비교
    return firebase.createdAt.isAfter(existing.createdAt);
  }

  /// 세트 할인 데이터를 Firebase에서 다운로드 (public 메서드)
  Future<void> downloadSetDiscountsFromFirebase(int eventId) async {
    return await _downloadSetDiscounts(eventId);
  }

  /// 세트 할인 거래 데이터를 다운로드합니다
  Future<void> _downloadSetDiscountTransactions(int eventId) async {
    try {
      LoggerUtils.logInfo('세트 할인 거래 데이터 다운로드 시작 (행사 $eventId)', tag: _tag);

      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('set_discount_transactions')
          .get();

      LoggerUtils.logInfo('세트 할인 거래 데이터 다운로드 시작 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);

      // 세트 할인 거래 데이터 파싱
      final transactions = <SetDiscountTransaction>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id);
          final transaction = SetDiscountTransaction.fromJson(data);
          transactions.add(transaction);
        } catch (e) {
          LoggerUtils.logWarning('세트 할인 거래 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      // 배치 처리로 한번에 저장 (효율적)
      if (transactions.isNotEmpty) {
        await _saveSetDiscountTransactionsBatchToLocal(transactions);
      }

      LoggerUtils.logInfo('세트 할인 거래 데이터 다운로드 완료 (행사 $eventId): ${transactions.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('세트 할인 거래 데이터 다운로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 가상상품 데이터를 다운로드합니다 (배치 처리 최적화)
  Future<void> _downloadVirtualProducts(int eventId) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('virtual_products')
          .get();

      LoggerUtils.logInfo('가상상품 데이터 다운로드 시작 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);

      // 가상상품 데이터 파싱
      final virtualProducts = <PrepaymentVirtualProduct>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id);
          final virtualProduct = PrepaymentVirtualProduct.fromMap(data);
          virtualProducts.add(virtualProduct);
        } catch (e) {
          LoggerUtils.logWarning('가상상품 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      // 배치 처리로 한번에 저장 (효율적)
      if (virtualProducts.isNotEmpty) {
        await _saveVirtualProductsBatchToLocal(virtualProducts);
      }

      LoggerUtils.logInfo('가상상품 데이터 다운로드 완료 (행사 $eventId): ${virtualProducts.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('가상상품 데이터 다운로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 가상상품들을 배치로 로컬 DB에 저장 (효율적인 방식)
  Future<void> _saveVirtualProductsBatchToLocal(List<PrepaymentVirtualProduct> virtualProducts) async {
    try {
      LoggerUtils.logInfo('가상상품 배치 저장 시작: ${virtualProducts.length}개', tag: _tag);

      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        for (final virtualProduct in virtualProducts) {
          // 기존 가상상품 확인
          final existing = await txn.query(
            'prepayment_virtual_product',
            where: 'id = ?',
            whereArgs: [virtualProduct.id],
          );

          if (existing.isEmpty) {
            // 새로 삽입 (실시간 동기화 트리거 안함)
            await txn.insert('prepayment_virtual_product', virtualProduct.toMap());
          } else {
            // 업데이트 (실시간 동기화 트리거 안함)
            await txn.update(
              'prepayment_virtual_product',
              virtualProduct.toMap(),
              where: 'id = ?',
              whereArgs: [virtualProduct.id],
            );
          }
        }
      }, taskName: 'saveVirtualProductsBatch');

      LoggerUtils.logInfo('가상상품 배치 저장 완료: ${virtualProducts.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('가상상품 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 연동내역 데이터를 다운로드합니다 (배치 처리 최적화)
  Future<void> _downloadProductLinks(int eventId) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('product_links')
          .get();

      LoggerUtils.logInfo('연동내역 데이터 다운로드 시작 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);

      // 연동내역 데이터 파싱
      final productLinks = <PrepaymentProductLink>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          final link = PrepaymentProductLink.fromJson(data);
          productLinks.add(link);
        } catch (e) {
          LoggerUtils.logWarning('연동내역 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      // 배치 처리로 한번에 저장 (효율적)
      if (productLinks.isNotEmpty) {
        await _saveProductLinksBatchToLocal(productLinks);
      }

      LoggerUtils.logInfo('연동내역 데이터 다운로드 완료 (행사 $eventId): ${productLinks.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('연동내역 데이터 다운로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 연동내역들을 배치로 로컬 DB에 저장 (효율적인 방식)
  Future<void> _saveProductLinksBatchToLocal(List<PrepaymentProductLink> productLinks) async {
    try {
      LoggerUtils.logInfo('연동내역 배치 저장 시작: ${productLinks.length}개', tag: _tag);

      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        for (final link in productLinks) {
          // 기존 연동내역 확인
          final existing = await txn.query(
            'prepayment_product_link',
            where: 'virtualProductId = ? AND productId = ?',
            whereArgs: [link.virtualProductId, link.productId],
          );

          if (existing.isEmpty) {
            // 새로 삽입 (실시간 동기화 트리거 안함)
            await txn.insert('prepayment_product_link', link.toDatabaseMap());
          } else {
            // 업데이트 (실시간 동기화 트리거 안함)
            await txn.update(
              'prepayment_product_link',
              link.toDatabaseMap(),
              where: 'virtualProductId = ? AND productId = ?',
              whereArgs: [link.virtualProductId, link.productId],
            );
          }
        }
      }, taskName: 'saveProductLinksBatch');

      LoggerUtils.logInfo('연동내역 배치 저장 완료: ${productLinks.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('연동내역 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 세트 할인 거래 데이터를 배치로 로컬에 저장
  Future<void> _saveSetDiscountTransactionsBatchToLocal(List<SetDiscountTransaction> transactions) async {
    try {
      LoggerUtils.logInfo('세트 할인 거래 배치 저장 시작: ${transactions.length}개', tag: _tag);

      // 새 데이터 저장 (기존 데이터는 덮어쓰기)
      for (final transaction in transactions) {
        await _setDiscountTransactionRepository.insert(transaction);
      }

      LoggerUtils.logInfo('세트 할인 거래 배치 저장 완료: ${transactions.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('세트 할인 거래 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 재고현황(상품) 데이터만 다운로드합니다
  Future<void> downloadInventoryData({
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('재고현황 데이터 다운로드 시작', tag: _tag);
      onProgress?.call('재고현황 데이터를 다운로드하는 중...');

      final serverEvents = await getServerEvents();
      if (serverEvents.isEmpty) {
        onProgress?.call('서버에 행사 데이터가 없습니다.');
        return;
      }

      for (int i = 0; i < serverEvents.length; i++) {
        final event = serverEvents[i];
        if (event.id == null) continue;

        final progressPercent = ((i + 1) / serverEvents.length * 100).toInt();
        onProgress?.call('${event.name} 재고현황을 다운로드하는 중... ($progressPercent%)');

        try {
          await _downloadProducts(event.id!); // 이미지 포함 다운로드
        } catch (e) {
          LoggerUtils.logWarning('행사 ${event.name} 재고현황 다운로드 중 일부 실패', tag: _tag, error: e);
        }
      }

      onProgress?.call('재고현황 다운로드가 완료되었습니다!');
      LoggerUtils.logInfo('재고현황 데이터 다운로드 완료', tag: _tag);
    } catch (e) {
      final errorMsg = '재고현황 다운로드 실패: $e';
      LoggerUtils.logError(errorMsg, tag: _tag, error: e);
      onError?.call(errorMsg);
      rethrow;
    }
  }

  /// 선입금 데이터만 다운로드합니다
  Future<void> downloadPrepaymentData({
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('선입금 데이터 다운로드 시작', tag: _tag);
      onProgress?.call('선입금 데이터를 다운로드하는 중...');

      final serverEvents = await getServerEvents();
      if (serverEvents.isEmpty) {
        onProgress?.call('서버에 행사 데이터가 없습니다.');
        return;
      }

      for (int i = 0; i < serverEvents.length; i++) {
        final event = serverEvents[i];
        if (event.id == null) continue;

        final progressPercent = ((i + 1) / serverEvents.length * 100).toInt();
        onProgress?.call('${event.name} 선입금을 다운로드하는 중... ($progressPercent%)');

        try {
          await _downloadPrepayments(event.id!);
        } catch (e) {
          LoggerUtils.logWarning('행사 ${event.name} 선입금 다운로드 중 일부 실패', tag: _tag, error: e);
        }
      }

      onProgress?.call('선입금 다운로드가 완료되었습니다!');
      LoggerUtils.logInfo('선입금 데이터 다운로드 완료', tag: _tag);
    } catch (e) {
      final errorMsg = '선입금 다운로드 실패: $e';
      LoggerUtils.logError(errorMsg, tag: _tag, error: e);
      onError?.call(errorMsg);
      rethrow;
    }
  }

  /// 스마트 동기화 (변경사항 기반)를 수행합니다
  Future<void> performBidirectionalSync({
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('스마트 동기화 시작 (변경사항 기반)', tag: _tag);
      onProgress?.call('스마트 동기화를 시작합니다...');

      // 1단계: 서버 데이터 확인
      onProgress?.call('서버 연결 상태를 확인하는 중... (10%)');
      final hasServer = await hasServerData();

      if (!hasServer) {
        // 서버에 데이터가 없으면 로컬 데이터 업로드
        onProgress?.call('서버에 데이터가 없어 로컬 데이터를 업로드합니다... (50%)');
        await uploadAllData(
          onProgress: (msg) => onProgress?.call('업로드 중: $msg'),
          onError: onError,
        );
      } else {
        // 서버에 데이터가 있으면 서버 우선으로 최신 데이터 다운로드
        onProgress?.call('서버에서 최신 데이터를 다운로드하는 중... (50%)');
        await downloadAllData(
          onProgress: (msg) => onProgress?.call('다운로드 중: $msg'),
          onError: onError,
        );
        
        // 서버 데이터를 우선하므로 로컬 업로드는 실시간 동기화가 처리
        LoggerUtils.logInfo('서버 우선 다운로드 완료 - 실시간 동기화가 개별 변경사항 처리', tag: _tag);
      }

      onProgress?.call('스마트 동기화가 완료되었습니다! (100%)');
      LoggerUtils.logInfo('스마트 동기화 완료', tag: _tag);
    } catch (e) {
      final errorMsg = '스마트 동기화 실패: $e';
      LoggerUtils.logError(errorMsg, tag: _tag, error: e);
      onError?.call(errorMsg);
      rethrow;
    }
  }

  /// 행사별 가상상품 데이터를 가져옵니다
  Future<List<PrepaymentVirtualProduct>> _getVirtualProductsByEventId(int eventId) async {
    try {
      final db = await _databaseService.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'prepayment_virtual_product',
        where: 'eventId = ?',
        whereArgs: [eventId],
      );

      return maps.map((map) => PrepaymentVirtualProduct.fromMap(map)).toList();
    } catch (e) {
      LoggerUtils.logError('가상상품 데이터 조회 실패 (행사 $eventId)', tag: _tag, error: e);
      return [];
    }
  }


  
  /// 백그라운드 이미지 업로드 큐 프로세서 시작
  void _startImageUploadQueueProcessor() {
    _queueProcessingTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _processImageUploadQueue();
    });
  }

  /// 행사 이미지 업로드 큐 프로세서 시작
  void _startEventImageUploadQueueProcessor() {
    _eventQueueProcessingTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _processEventImageUploadQueue();
    });
  }
  
  /// 이미지 업로드 큐를 백그라운드에서 처리
  Future<void> _processImageUploadQueue() async {
    if (_isProcessingImageQueue || _imageUploadQueue.isEmpty) return;
    
    _isProcessingImageQueue = true;
    try {
      final task = _imageUploadQueue.removeAt(0);
      await _processImageUploadTask(task);
    } catch (e) {
      LoggerUtils.logError('이미지 업로드 큐 처리 실패', tag: _tag, error: e);
    } finally {
      _isProcessingImageQueue = false;
    }
  }

  /// 행사 이미지 업로드 큐를 백그라운드에서 처리
  Future<void> _processEventImageUploadQueue() async {
    if (_isProcessingEventImageQueue || _eventImageUploadQueue.isEmpty) return;
    
    _isProcessingEventImageQueue = true;
    try {
      final task = _eventImageUploadQueue.removeAt(0);
      await _processEventImageUploadTask(task);
    } catch (e) {
      LoggerUtils.logError('행사 이미지 업로드 큐 처리 실패', tag: _tag, error: e);
    } finally {
      _isProcessingEventImageQueue = false;
    }
  }
  
  /// 개별 이미지 업로드 작업 처리
  Future<void> _processImageUploadTask(_ImageUploadTask task) async {
    try {
      LoggerUtils.logInfo('백그라운드 이미지 업로드 시작: ${task.product.name}', tag: _tag);
      
      final uploadedImageUrl = await ImageSyncUtils.uploadProductImageWithCategoryId(
        task.product.eventId,
        task.product.categoryId,
        task.product.name,
        task.localImagePath,
      );
      
      if (uploadedImageUrl != null) {
        // Firestore의 상품 이미지 URL 업데이트
        await _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(task.product.eventId.toString())
            .collection('products')
            .doc(task.product.id.toString())
            .update({'imagePath': uploadedImageUrl});

        LoggerUtils.logInfo('백그라운드 이미지 업로드 완료: ${task.product.name}', tag: _tag);
      }
    } catch (e) {
      task.retryCount++;
      LoggerUtils.logError('이미지 업로드 실패 (재시도 ${task.retryCount}/3): ${task.product.name}', tag: _tag, error: e);
      
      // 3회 재시도 후에도 실패하면 포기
      if (task.retryCount < 3) {
        _imageUploadQueue.add(task);
      }
    }
  }

  /// 개별 행사 이미지 업로드 작업 처리
  Future<void> _processEventImageUploadTask(_EventImageUploadTask task) async {
    try {
      LoggerUtils.logInfo('백그라운드 행사 이미지 업로드 시작: ${task.event.name}', tag: _tag);
      
      final uploadedImageUrl = await ImageSyncUtils.uploadEventImage(
        task.event.id!,
        task.localImagePath,
      );
      
      if (uploadedImageUrl != null) {
        // Firestore의 행사 이미지 URL 업데이트
        await _firestore
            .collection('users')
            .doc(_currentUserId!)
            .collection('events')
            .doc(task.event.id.toString())
            .update({'imagePath': uploadedImageUrl});
            
        LoggerUtils.logInfo('백그라운드 행사 이미지 업로드 완료: ${task.event.name}', tag: _tag);
      }
    } catch (e) {
      task.retryCount++;
      LoggerUtils.logError('행사 이미지 업로드 실패 (재시도 ${task.retryCount}/3): ${task.event.name}', tag: _tag, error: e);
      
      // 3회 재시도 후에도 실패하면 포기
      if (task.retryCount < 3) {
        _eventImageUploadQueue.add(task);
      }
    }
  }
  
  /// 이미지 업로드 작업을 백그라운드 큐에 추가
  void _addImageUploadTask(Product product, String localImagePath) {
    final task = _ImageUploadTask(
      product: product,
      localImagePath: localImagePath,
      createdAt: DateTime.now(),
    );
    _imageUploadQueue.add(task);
    LoggerUtils.logInfo('이미지 업로드 작업 큐에 추가: ${product.name}', tag: _tag);
  }

  /// 행사 이미지 업로드 작업을 백그라운드 큐에 추가
  void _addEventImageUploadTask(Event event, String localImagePath) {
    final task = _EventImageUploadTask(
      event: event,
      localImagePath: localImagePath,
      createdAt: DateTime.now(),
    );
    _eventImageUploadQueue.add(task);
    LoggerUtils.logInfo('행사 이미지 업로드 작업 큐에 추가: ${event.name}', tag: _tag);
  }
  
  /// 이미지 다운로드 상태 강제 정리 (메모리 누수 방지)
  void _forceCleanupImageDownloadState() {
    LoggerUtils.logWarning('이미지 다운로드 상태 강제 정리 실행', tag: _tag);
    _downloadingImages.clear();
    _currentDownloads = 0;
  }

  /// 이미지 다운로드 상태 확인 및 정리
  void _checkAndCleanupImageDownloadState() {
    // 다운로드 중인 이미지 수와 카운터가 일치하지 않으면 정리
    if (_downloadingImages.length != _currentDownloads) {
      LoggerUtils.logWarning(
        '이미지 다운로드 상태 불일치 감지 - Set: ${_downloadingImages.length}, Counter: $_currentDownloads',
        tag: _tag
      );
      _currentDownloads = _downloadingImages.length;
    }

    // 너무 오래된 다운로드 상태가 있으면 정리 (안전장치)
    if (_currentDownloads > 0) {
      LoggerUtils.logDebug('현재 다운로드 중인 이미지: $_currentDownloads개', tag: _tag);
    }
  }

  /// 예상되는 로컬 이미지 경로 생성
  Future<String> _getExpectedLocalImagePath(int eventId, int productId) async {
    final appDir = await getApplicationDocumentsDirectory();
    return '${appDir.path}/product_images/$eventId/${productId}_image.jpg';
  }

  /// 서비스 정리 (메모리 누수 방지)
  void dispose() {
    _queueProcessingTimer?.cancel();
    _eventQueueProcessingTimer?.cancel();
    _imageUploadQueue.clear();
    _eventImageUploadQueue.clear();

    // 이미지 다운로드 상태 정리
    _forceCleanupImageDownloadState();

    LoggerUtils.logInfo('DataSyncService 정리 완료', tag: _tag);
  }

  // ============================================================================
  // 사용자 설정 동기화 메서드들
  // ============================================================================

  /// 사용자 설정을 Firebase에 업로드
  Future<void> uploadUserSettings(UserSettings settings) async {
    return await executeWithAuthErrorHandling('사용자 설정 업로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('사용자 설정 업로드 시작', tag: _tag);

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('settings')
          .doc('user_settings')
          .set(settings.toFirebaseMap());

      LoggerUtils.logInfo('사용자 설정 업로드 완료', tag: _tag);
    });
  }

  /// 개별 체크리스트 템플릿을 즉시 Firebase에 업로드 (실시간 동기화용)
  Future<void> uploadSingleChecklistTemplate(ChecklistTemplate template) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 업로드 건너뛰기
      if (!_isRealtimeSyncEnabled) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 체크리스트 템플릿 업로드를 건너뜁니다: ${template.title}', tag: _tag);
        return;
      }

      // ID가 null인 경우 오류 발생
      if (template.id == null) {
        throw Exception('체크리스트 템플릿 ID가 null입니다: ${template.title}');
      }

      LoggerUtils.logInfo('개별 체크리스트 템플릿 즉시 업로드 시작: ${template.title}', tag: _tag);

      final templateData = template.toJson();
      templateData.remove('id'); // Firestore 문서 ID로 사용할 것이므로 제거

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('checklist_templates')
          .doc(template.id.toString())
          .set(templateData);

      LoggerUtils.logInfo('개별 체크리스트 템플릿 즉시 업로드 완료: ${template.title}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 체크리스트 템플릿 즉시 업로드 실패: ${template.title}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 개별 체크리스트 아이템을 즉시 Firebase에 업로드 (실시간 동기화용)
  Future<void> uploadSingleChecklistItem(ChecklistItem item) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      // 실시간 동기화가 비활성화된 경우 또는 플랜에서 지원하지 않는 경우 업로드 건너뛰기
      final canUseRealtimeSync = await _canUseRealtimeSync;
      if (!_isRealtimeSyncEnabled || !canUseRealtimeSync) {
        LoggerUtils.logInfo('실시간 동기화가 비활성화되어 체크리스트 아이템 업로드를 건너뜁니다: templateId=${item.templateId}, eventId=${item.eventId}', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('개별 체크리스트 아이템 즉시 업로드 시작: templateId=${item.templateId}, eventId=${item.eventId}', tag: _tag);

      final itemData = item.toJson();
      itemData.remove('id'); // Firestore 문서 ID로 사용할 것이므로 제거

      // templateId와 eventId 조합을 문서 ID로 사용
      final documentId = '${item.templateId}_${item.eventId}';

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(item.eventId.toString())
          .collection('checklist_items')
          .doc(documentId)
          .set(itemData);

      LoggerUtils.logInfo('개별 체크리스트 아이템 즉시 업로드 완료: templateId=${item.templateId}, eventId=${item.eventId}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('개별 체크리스트 아이템 즉시 업로드 실패: templateId=${item.templateId}, eventId=${item.eventId}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// Firebase에서 체크리스트 템플릿 삭제
  Future<void> deleteChecklistTemplate(int templateId) async {
    try {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('체크리스트 템플릿 Firebase 삭제 시작: $templateId', tag: _tag);

      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('checklist_templates')
          .doc(templateId.toString())
          .delete();

      LoggerUtils.logInfo('체크리스트 템플릿 Firebase 삭제 완료: $templateId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 Firebase 삭제 실패: $templateId', tag: _tag, error: e);
      rethrow;
    }
  }

  /// Firebase에서 최신 체크리스트 템플릿 목록을 다운로드하여 로컬 DB와 동기화
  Future<void> downloadChecklistTemplatesFromFirebase() async {
    return await executeWithAuthErrorHandling('체크리스트 템플릿 목록 다운로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('Firebase에서 최신 체크리스트 템플릿 목록 다운로드 시작', tag: _tag);
      await _downloadChecklistTemplates();
      LoggerUtils.logInfo('Firebase에서 최신 체크리스트 템플릿 목록 다운로드 완료', tag: _tag);
    });
  }

  /// Firebase에서 사용자 설정 다운로드
  Future<UserSettings?> downloadUserSettings() async {
    return await executeWithAuthErrorHandling('사용자 설정 다운로드', () async {
      if (!isUserLoggedIn) throw Exception('로그인이 필요합니다');

      LoggerUtils.logInfo('사용자 설정 다운로드 시작', tag: _tag);

      final doc = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('settings')
          .doc('user_settings')
          .get();

      if (!doc.exists || doc.data() == null) {
        LoggerUtils.logInfo('저장된 사용자 설정이 없습니다', tag: _tag);
        return null;
      }

      final settings = UserSettingsExtensions.fromFirebaseMap(doc.data()!);
      LoggerUtils.logInfo('사용자 설정 다운로드 완료: lastWorkspaceId=${settings.lastWorkspaceId}', tag: _tag);

      return settings;
    });
  }

  /// 체크리스트 템플릿 데이터를 다운로드합니다 (배치 처리 최적화)
  Future<void> _downloadChecklistTemplates() async {
    return await executeWithAuthErrorHandling('체크리스트 템플릿 데이터 다운로드', () async {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('checklist_templates')
          .get();

      LoggerUtils.logInfo('체크리스트 템플릿 데이터 다운로드 시작: ${snapshot.docs.length}개', tag: _tag);

      // 체크리스트 템플릿 데이터 파싱
      final templates = <ChecklistTemplate>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = int.parse(doc.id);
          final template = ChecklistTemplate.fromJson(data);
          templates.add(template);
        } catch (e) {
          LoggerUtils.logWarning('체크리스트 템플릿 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      // 배치 처리로 한번에 저장 (효율적)
      if (templates.isNotEmpty) {
        await _saveChecklistTemplatesBatchToLocal(templates);
      }

      LoggerUtils.logInfo('체크리스트 템플릿 데이터 다운로드 완료: ${templates.length}개', tag: _tag);
    });
  }

  /// 체크리스트 템플릿들을 배치로 로컬 DB에 저장 (효율적인 방식)
  Future<void> _saveChecklistTemplatesBatchToLocal(List<ChecklistTemplate> templates) async {
    try {
      LoggerUtils.logInfo('체크리스트 템플릿 배치 저장 시작: ${templates.length}개', tag: _tag);

      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        for (final template in templates) {
          // 기존 템플릿 확인
          final existing = await txn.query(
            'checklist_templates',
            where: 'id = ?',
            whereArgs: [template.id!],
          );

          if (existing.isEmpty) {
            // 새로 삽입 (실시간 동기화 트리거 안함)
            await txn.insert('checklist_templates', template.toMap());
          } else {
            // 업데이트 (실시간 동기화 트리거 안함)
            await txn.update(
              'checklist_templates',
              template.toMap(),
              where: 'id = ?',
              whereArgs: [template.id!],
            );
          }
        }
      }, taskName: 'saveChecklistTemplatesBatch');

      LoggerUtils.logInfo('체크리스트 템플릿 배치 저장 완료: ${templates.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 체크리스트 템플릿 데이터를 업로드합니다
  Future<void> _uploadChecklistTemplates() async {
    return await executeWithAuthErrorHandling('체크리스트 템플릿 데이터 업로드', () async {
      final templates = await _checklistRepository.getAllTemplates();
      LoggerUtils.logInfo('체크리스트 템플릿 데이터 업로드 시작: ${templates.length}개', tag: _tag);

      for (final template in templates) {
        try {
          // ID가 null인 경우 건너뛰기
          if (template.id == null) {
            LoggerUtils.logWarning('체크리스트 템플릿 ID가 null입니다: ${template.title}', tag: _tag);
            continue;
          }

          final templateData = template.toJson();
          templateData.remove('id'); // Firestore 문서 ID로 사용할 것이므로 제거

          await _firestore
              .collection('users')
              .doc(_currentUserId!)
              .collection('checklist_templates')
              .doc(template.id.toString())
              .set(templateData);

          LoggerUtils.logDebug('체크리스트 템플릿 업로드 완료: ${template.title}', tag: _tag);
        } catch (e) {
          LoggerUtils.logWarning('체크리스트 템플릿 업로드 실패: ${template.title}', tag: _tag, error: e);
        }
      }

      LoggerUtils.logInfo('체크리스트 템플릿 데이터 업로드 완료: ${templates.length}개', tag: _tag);
    });
  }

  /// 체크리스트 아이템 데이터를 업로드합니다
  Future<void> _uploadChecklistItems(int eventId) async {
    return await executeWithAuthErrorHandling('체크리스트 아이템 데이터 업로드', () async {
      final items = await _checklistRepository.getItemsByEventId(eventId);
      LoggerUtils.logInfo('체크리스트 아이템 데이터 업로드 시작 (행사 $eventId): ${items.length}개', tag: _tag);

      for (final item in items) {
        try {
          final itemData = item.toJson();
          itemData.remove('id'); // Firestore 문서 ID로 사용할 것이므로 제거

          // templateId와 eventId 조합을 문서 ID로 사용
          final documentId = '${item.templateId}_${item.eventId}';

          await _firestore
              .collection('users')
              .doc(_currentUserId!)
              .collection('events')
              .doc(eventId.toString())
              .collection('checklist_items')
              .doc(documentId)
              .set(itemData);

          LoggerUtils.logDebug('체크리스트 아이템 업로드 완료: templateId=${item.templateId}', tag: _tag);
        } catch (e) {
          LoggerUtils.logWarning('체크리스트 아이템 업로드 실패: templateId=${item.templateId}', tag: _tag, error: e);
        }
      }

      LoggerUtils.logInfo('체크리스트 아이템 데이터 업로드 완료 (행사 $eventId): ${items.length}개', tag: _tag);
    });
  }

  /// 체크리스트 아이템 데이터를 다운로드합니다 (배치 처리 최적화)
  Future<void> _downloadChecklistItems(int eventId) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('events')
          .doc(eventId.toString())
          .collection('checklist_items')
          .get();

      LoggerUtils.logInfo('체크리스트 아이템 데이터 다운로드 시작 (행사 $eventId): ${snapshot.docs.length}개', tag: _tag);

      // 체크리스트 아이템 데이터 파싱
      final items = <ChecklistItem>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          // 문서 ID에서 templateId와 eventId 추출 (templateId_eventId 형태)
          final parts = doc.id.split('_');
          if (parts.length == 2) {
            data['templateId'] = int.parse(parts[0]);
            data['eventId'] = int.parse(parts[1]);
          }
          final item = ChecklistItem.fromJson(data);
          items.add(item);
        } catch (e) {
          LoggerUtils.logWarning('체크리스트 아이템 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      // 배치 처리로 한번에 저장 (효율적)
      if (items.isNotEmpty) {
        await _saveChecklistItemsBatchToLocal(items);
      }

      LoggerUtils.logInfo('체크리스트 아이템 데이터 다운로드 완료 (행사 $eventId): ${items.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 아이템 데이터 다운로드 실패 (행사 $eventId)', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 체크리스트 아이템들을 배치로 로컬 DB에 저장 (효율적인 방식)
  Future<void> _saveChecklistItemsBatchToLocal(List<ChecklistItem> items) async {
    try {
      LoggerUtils.logInfo('체크리스트 아이템 배치 저장 시작: ${items.length}개', tag: _tag);

      // 실시간 동기화 트리거 방지를 위해 직접 데이터베이스에 접근
      await _databaseService.safeTransaction((txn) async {
        for (final item in items) {
          // 기존 아이템 확인
          final existing = await txn.query(
            'checklist_items',
            where: 'templateId = ? AND eventId = ?',
            whereArgs: [item.templateId, item.eventId],
          );

          if (existing.isEmpty) {
            // 새로 삽입 (실시간 동기화 트리거 안함)
            await txn.insert('checklist_items', item.toMap());
          } else {
            // 업데이트 (실시간 동기화 트리거 안함)
            await txn.update(
              'checklist_items',
              item.toMap(),
              where: 'templateId = ? AND eventId = ?',
              whereArgs: [item.templateId, item.eventId],
            );
          }
        }
      }, taskName: 'saveChecklistItemsBatch');

      LoggerUtils.logInfo('체크리스트 아이템 배치 저장 완료: ${items.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 아이템 배치 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }


}

/// 백그라운드 이미지 업로드 작업을 위한 클래스
class _ImageUploadTask {
  final Product product;
  final String localImagePath;
  final DateTime createdAt;
  int retryCount;

  _ImageUploadTask({
    required this.product,
    required this.localImagePath,
    required this.createdAt,
  }) : retryCount = 0;
}

/// 행사 이미지 업로드 작업을 위한 클래스
class _EventImageUploadTask {
  final Event event;
  final String localImagePath;
  final DateTime createdAt;
  int retryCount;

  _EventImageUploadTask({
    required this.event,
    required this.localImagePath,
    required this.createdAt,
  }) : retryCount = 0;
}




