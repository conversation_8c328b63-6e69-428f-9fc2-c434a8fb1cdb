rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // ===== 사용자 기본 문서 =====
    match /users/{userId} {
      // 본인 데이터만 읽기/쓰기 가능
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // 닉네임 중복 확인을 위한 제한적 읽기 허용
      // 인증된 사용자만 닉네임 필드 읽기 가능
      allow read: if request.auth != null;

      // 전화번호 업데이트 시 추가 보안 검증
      allow update: if request.auth != null
        && request.auth.uid == userId
        && (
          // 전화번호 필드가 변경되지 않거나
          !('phone' in request.resource.data) ||
          // 전화번호 형식이 올바른 경우만 허용
          (request.resource.data.phone is string &&
           request.resource.data.phone.matches('^01[0-9]-[0-9]{4}-[0-9]{4}$'))
        );

      // ===== 사용자별 모든 하위 컬렉션 (통합 관리) =====
      match /{subcollection=**} {
        // 본인 데이터만 접근 가능
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    // ===== SMS 인증 (전화번호를 키로 사용) =====
    match /sms_verifications/{phoneNumber} {
      // 인증 요청한 사용자만 접근 가능
      allow read, write: if request.auth != null &&
        resource.data.uid == request.auth.uid;

      // 새 인증 요청 생성 (Functions에서만)
      allow create: if false; // Functions 전용
    }

    // ===== 핸드폰 번호 중복 방지 (새로 추가) =====
    match /phone_numbers/{phoneNumber} {
      // 해당 번호를 소유한 사용자만 읽기 가능
      allow read: if request.auth != null &&
        resource.data.userId == request.auth.uid;

      // 생성/업데이트는 Functions에서만
      allow write: if false; // Functions 전용
    }

    // ===== 행사 권한 관리 =====
    match /event_permissions/{eventId} {
      // 권한 컬렉션 읽기 (인증된 사용자만)
      allow read: if request.auth != null;

      match /users/{userId} {
        // 본인 권한 정보는 항상 읽기/쓰기 가능
        allow read, write: if request.auth != null && request.auth.uid == userId;

        // 행사 소유자는 모든 권한 정보 읽기/쓰기 가능
        allow read, write: if request.auth != null &&
          exists(/databases/$(database)/documents/users/$(request.auth.uid)/events/$(eventId));

        // 권한 생성은 제한적으로 허용 (실제 검증은 클라이언트에서)
        allow create: if request.auth != null &&
          request.resource.data.userId == request.auth.uid;
      }
    }

    // ===== 행사 초대 관리 =====
    match /event_invitations/{invitationId} {
      // 인증된 사용자만 읽기 가능
      allow read: if request.auth != null;

      // 초대 생성/수정은 제한적으로 허용
      allow write: if request.auth != null;
    }

    // ===== 관리자 전용 =====
    match /admin/{document=**} {
      allow read, write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // ===== 공개 데이터 =====
    match /public/{document=**} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
    }

    // ===== 사용량 통계 =====
    match /usage_stats/{document=**} {
      allow read: if request.auth != null &&
        request.auth.token.email == '<EMAIL>';
      allow write: if false; // Functions 전용
    }

    // ===== 기본 거부 =====
    match /{document=**} {
      allow read, write: if false;
    }
  }
}